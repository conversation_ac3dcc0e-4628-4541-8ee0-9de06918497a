Augment VIP - Windows版本使用说明
=====================================

这是一个集成的单文件工具，用于管理VS Code中的Augment相关数据。

系统要求：
- Windows操作系统
- Python 3.6或更高版本

快速开始：
1. 确保已安装Python
2. 可选：安装colorama库以获得彩色输出
   pip install colorama
3. 运行工具

命令说明：

1. 清理数据库：
   python main.py clean
   
   功能：删除VS Code数据库中所有包含"augment"的条目
   
2. 修改遥测ID：
   python main.py modify-ids
   
   功能：生成新的随机遥测ID替换现有ID
   
3. 运行所有工具：
   python main.py all
   
   功能：依次执行数据库清理和ID修改

4. 查看帮助：
   python main.py --help

安全特性：
- 所有操作前都会自动创建备份文件
- 备份文件保存在原文件同目录下，扩展名为.backup
- 如果操作失败，会尝试从备份恢复

注意事项：
- 运行前请确保VS Code已关闭
- 修改遥测ID后需要重启VS Code
- 如果遇到权限问题，请以管理员身份运行

文件位置：
- VS Code数据库：%APPDATA%\Code\User\globalStorage\state.vscdb
- VS Code配置：%APPDATA%\Code\User\globalStorage\storage.json

如有问题，请检查：
1. Python是否正确安装
2. APPDATA环境变量是否存在
3. VS Code是否已安装并使用过
4. 是否有足够的文件访问权限
