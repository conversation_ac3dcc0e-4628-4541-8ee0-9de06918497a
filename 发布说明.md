# Augment VIP v1.0.0 发布说明

## 📦 打包完成

Augment VIP已成功打包为Windows可执行文件，无需安装Python环境即可直接使用。

## 📁 发布文件结构

```
dist/
├── augment-vip.exe      # 主程序（独立可执行文件）
├── 启动.bat             # 交互式启动脚本
├── 清理数据库.bat       # 直接清理数据库
├── 修改ID.bat          # 直接修改遥测ID
├── 运行所有.bat         # 运行所有功能
├── README.md           # 项目说明文档
├── 使用说明.txt         # 中文使用说明
└── requirements.txt    # 依赖列表（仅供参考）
```

## 🚀 使用方法

### 方法一：交互式菜单（推荐新手）
1. 双击 `启动.bat`
2. 根据菜单提示选择功能
3. 按提示操作

### 方法二：直接运行功能脚本
- 双击 `清理数据库.bat` - 直接清理VS Code数据库
- 双击 `修改ID.bat` - 直接修改遥测ID
- 双击 `运行所有.bat` - 运行所有功能

### 方法三：命令行使用
```cmd
# 进入dist目录
cd dist

# 清理数据库
augment-vip.exe clean

# 修改遥测ID
augment-vip.exe modify-ids

# 运行所有工具
augment-vip.exe all

# 查看帮助
augment-vip.exe --help

# 查看版本
augment-vip.exe --version
```

## ✨ 功能特性

### 🗃️ 数据库清理
- 自动查找Windows系统下的VS Code数据库
- 删除所有包含"augment"的条目
- 操作前自动创建备份
- 显示删除的条目数量

### 🔐 遥测ID修改
- 生成64字符随机十六进制machineId
- 生成随机UUID v4格式的devDeviceId
- 自动备份原配置文件
- 显示新生成的ID值

### 🛡️ 安全特性
- 所有操作前自动创建备份文件
- 备份文件保存在原文件同目录，扩展名为.backup
- 操作失败时自动尝试从备份恢复
- 详细的操作日志和错误提示

## 📋 系统要求

- **操作系统**: Windows 7/8/10/11
- **架构**: x64 (64位)
- **权限**: 建议以管理员身份运行
- **VS Code**: 需要已安装并使用过VS Code

## ⚠️ 注意事项

1. **运行前关闭VS Code**: 确保VS Code完全关闭后再运行工具
2. **管理员权限**: 如遇权限问题，请右键"以管理员身份运行"
3. **重启VS Code**: 修改遥测ID后需要重启VS Code才能生效
4. **备份重要**: 工具会自动备份，但建议手动备份重要数据
5. **杀毒软件**: 部分杀毒软件可能误报，请添加到白名单

## 🔍 文件位置

工具会自动查找以下位置的VS Code文件：
- **数据库文件**: `%APPDATA%\Code\User\globalStorage\state.vscdb`
- **配置文件**: `%APPDATA%\Code\User\globalStorage\storage.json`
- **备份文件**: 原文件同目录下的`.backup`文件

## 🐛 故障排除

### 问题1: 未找到VS Code数据库
**解决方案**:
- 确保VS Code已安装并至少运行过一次
- 检查APPDATA环境变量是否正确
- 尝试以管理员身份运行

### 问题2: 权限被拒绝
**解决方案**:
- 右键选择"以管理员身份运行"
- 确保VS Code已完全关闭
- 检查文件是否被其他程序占用

### 问题3: 操作失败
**解决方案**:
- 查看错误信息提示
- 检查备份文件是否存在
- 手动从备份文件恢复

## 📞 技术支持

如遇问题，请检查：
1. 系统是否满足要求
2. VS Code是否已关闭
3. 是否有足够的权限
4. 错误信息的具体内容

## 🎯 版本信息

- **版本**: v1.0.0
- **构建日期**: 2025-06-28
- **Python版本**: 3.13.2
- **PyInstaller版本**: 6.14.1
- **支持系统**: Windows x64

---

**感谢使用 Augment VIP！**
