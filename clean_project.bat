@echo off
chcp 65001 >nul
echo 项目清理脚本
echo =============
echo.
echo 此脚本将清理构建过程中产生的临时文件
echo 保留源代码和最终的dist目录
echo.
echo 将要删除的文件/目录:
echo - build/ (构建临时目录)
echo - *.spec (PyInstaller规格文件)
echo - __pycache__/ (Python缓存目录)
echo.
set /p confirm="确认清理? (y/N): "
if /i not "%confirm%"=="y" (
    echo 取消清理
    pause
    exit /b 0
)

echo.
echo 开始清理...

if exist "build" (
    echo 删除 build/ 目录...
    rmdir /s /q "build"
)

if exist "*.spec" (
    echo 删除 *.spec 文件...
    del "*.spec"
)

if exist "__pycache__" (
    echo 删除 __pycache__/ 目录...
    rmdir /s /q "__pycache__"
)

echo.
echo 清理完成！
echo.
echo 保留的文件:
echo - main.py (源代码)
echo - build.py (打包脚本)
echo - quick_build.bat (快速打包脚本)
echo - requirements.txt (依赖列表)
echo - README.md (项目说明)
echo - 使用说明.txt (中文说明)
echo - 发布说明.md (发布说明)
echo - dist/ (最终发布目录)
echo.
pause
