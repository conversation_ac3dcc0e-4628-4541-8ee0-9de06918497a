@echo off
chcp 65001 >nul
echo Augment VIP 快速打包脚本
echo ========================
echo.

echo [1/4] 检查Python环境...
python --version
if errorlevel 1 (
    echo [ERROR] Python未安装或未添加到PATH
    pause
    exit /b 1
)

echo [2/4] 安装依赖包...
pip install pyinstaller colorama

echo [3/4] 清理旧文件...
if exist "dist" rmdir /s /q "dist"
if exist "build" rmdir /s /q "build"
if exist "*.spec" del "*.spec"

echo [4/4] 开始打包...
pyinstaller --onefile --console --name "augment-vip" --add-data "README.md;." --add-data "使用说明.txt;." main.py

if errorlevel 1 (
    echo [ERROR] 打包失败
    pause
    exit /b 1
)

echo.
echo [SUCCESS] 打包完成！
echo 输出文件: dist\augment-vip.exe
echo.

echo 创建启动脚本...
echo @echo off > "dist\启动.bat"
echo chcp 65001 ^>nul >> "dist\启动.bat"
echo echo Augment VIP - Windows版本 >> "dist\启动.bat"
echo echo ======================== >> "dist\启动.bat"
echo echo. >> "dist\启动.bat"
echo echo 请选择操作: >> "dist\启动.bat"
echo echo 1. 清理VS Code数据库 >> "dist\启动.bat"
echo echo 2. 修改遥测ID >> "dist\启动.bat"
echo echo 3. 运行所有工具 >> "dist\启动.bat"
echo echo 4. 查看帮助 >> "dist\启动.bat"
echo echo 5. 退出 >> "dist\启动.bat"
echo echo. >> "dist\启动.bat"
echo set /p choice="请输入选择 (1-5): " >> "dist\启动.bat"
echo. >> "dist\启动.bat"
echo if "%%choice%%"=="1" ( >> "dist\启动.bat"
echo     augment-vip.exe clean >> "dist\启动.bat"
echo ^) else if "%%choice%%"=="2" ( >> "dist\启动.bat"
echo     augment-vip.exe modify-ids >> "dist\启动.bat"
echo ^) else if "%%choice%%"=="3" ( >> "dist\启动.bat"
echo     augment-vip.exe all >> "dist\启动.bat"
echo ^) else if "%%choice%%"=="4" ( >> "dist\启动.bat"
echo     augment-vip.exe --help >> "dist\启动.bat"
echo ^) else if "%%choice%%"=="5" ( >> "dist\启动.bat"
echo     exit >> "dist\启动.bat"
echo ^) else ( >> "dist\启动.bat"
echo     echo 无效选择，请重新运行 >> "dist\启动.bat"
echo ^) >> "dist\启动.bat"
echo. >> "dist\启动.bat"
echo echo. >> "dist\启动.bat"
echo pause >> "dist\启动.bat"

echo 创建快捷脚本...
echo @echo off > "dist\清理数据库.bat"
echo chcp 65001 ^>nul >> "dist\清理数据库.bat"
echo augment-vip.exe clean >> "dist\清理数据库.bat"
echo pause >> "dist\清理数据库.bat"

echo @echo off > "dist\修改ID.bat"
echo chcp 65001 ^>nul >> "dist\修改ID.bat"
echo augment-vip.exe modify-ids >> "dist\修改ID.bat"
echo pause >> "dist\修改ID.bat"

echo @echo off > "dist\运行所有.bat"
echo chcp 65001 ^>nul >> "dist\运行所有.bat"
echo augment-vip.exe all >> "dist\运行所有.bat"
echo pause >> "dist\运行所有.bat"

echo.
echo [SUCCESS] 所有文件已创建完成！
echo.
echo 输出目录: dist\
echo 主程序: augment-vip.exe
echo 启动脚本: 启动.bat
echo 快捷脚本: 清理数据库.bat, 修改ID.bat, 运行所有.bat
echo.
echo 按任意键打开输出目录...
pause >nul
explorer dist
