#!/usr/bin/env python3
"""
简化的打包脚本
将main.py打包成exe文件
"""

import os
import sys
import subprocess
import shutil

def main():
    print("🔧 Augment VIP 打包工具")
    print("=" * 30)
    
    # 检查源文件
    if not os.path.exists("main.py"):
        print("[错误] 未找到 main.py 文件")
        input("按回车键退出...")
        sys.exit(1)
    
    # 检查PyInstaller
    try:
        import PyInstaller
        print(f"[信息] PyInstaller版本: {PyInstaller.__version__}")
    except ImportError:
        print("[信息] 正在安装PyInstaller...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
    
    # 清理旧文件
    if os.path.exists("dist"):
        print("[信息] 清理旧的dist目录...")
        shutil.rmtree("dist")
    
    if os.path.exists("build"):
        print("[信息] 清理旧的build目录...")
        shutil.rmtree("build")
    
    # 打包
    print("[信息] 开始打包...")
    cmd = [
        sys.executable, "-m", "PyInstaller",
        "--onefile",
        "--windowed",  # 无控制台窗口
        "--name", "AugmentVIP",
        "main.py"
    ]
    
    try:
        subprocess.check_call(cmd)
        print("[成功] 打包完成！")
        print("输出文件: dist/AugmentVIP.exe")
        print("\n双击 AugmentVIP.exe 启动GUI界面")
    except subprocess.CalledProcessError:
        print("[错误] 打包失败")
        sys.exit(1)
    
    input("按回车键退出...")

if __name__ == "__main__":
    main()
