#!/usr/bin/env python3
"""
Augment VIP 打包脚本
"""

import os
import sys
import subprocess
import shutil

def main():
    print("🔧 Augment VIP 打包工具")
    print("=" * 30)

    # 检查PyInstaller
    try:
        import PyInstaller
        print(f"[信息] PyInstaller版本: {PyInstaller.__version__}")
    except ImportError:
        print("[信息] 正在安装PyInstaller...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])

    # 清理旧文件
    for folder in ["dist", "build"]:
        if os.path.exists(folder):
            print(f"[信息] 清理 {folder} 目录...")
            shutil.rmtree(folder)

    # 打包
    print("[信息] 开始打包...")
    cmd = [
        sys.executable, "-m", "PyInstaller",
        "--onefile",
        "--windowed",
        "--name", "AugmentVIP",
        "main.py"
    ]

    try:
        subprocess.check_call(cmd)
        print("[成功] 打包完成！")

        # 清理多余文件
        print("[信息] 清理多余文件...")

        # 删除build目录
        if os.path.exists("build"):
            shutil.rmtree("build")
            print("[信息] 已删除 build 目录")

        # 删除spec文件
        spec_files = [f for f in os.listdir(".") if f.endswith(".spec")]
        for spec_file in spec_files:
            os.remove(spec_file)
            print(f"[信息] 已删除 {spec_file}")

        # 删除源文件和打包脚本
        files_to_remove = ["main.py", "pack.py", "requirements.txt"]
        for file_name in files_to_remove:
            if os.path.exists(file_name):
                os.remove(file_name)
                print(f"[信息] 已删除 {file_name}")

        print("\n[成功] 清理完成！")
        print("最终文件: dist/AugmentVIP.exe")
        print("\n双击 AugmentVIP.exe 启动一键重置工具")

    except subprocess.CalledProcessError:
        print("[错误] 打包失败")
        sys.exit(1)

    input("按回车键退出...")

if __name__ == "__main__":
    main()
