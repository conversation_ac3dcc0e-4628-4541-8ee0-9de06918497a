# Augment VIP - Windows版本

![License](https://img.shields.io/badge/license-MIT-blue.svg)
![Version](https://img.shields.io/badge/version-1.0.0-green.svg)

Augment VIP Windows版本集成工具，用于管理和清理VS Code数据库中与Augment相关的条目。

## 🚀 功能特性

- **数据库清理**: 删除VS Code数据库中的Augment相关条目
- **遥测ID修改**: 生成随机遥测ID以保护隐私
- **Windows专用**: 专为Windows系统优化
- **单文件集成**: 所有功能集成在一个main.py文件中
- **安全操作**: 操作前自动创建备份
- **彩色输出**: 清晰的彩色控制台输出

## 📋 系统要求

- Windows操作系统
- Python 3.6 或更高版本
- 可选: colorama库（用于彩色输出）

## 💻 安装方法

### 快速安装

1. 下载项目文件：
```bash
git clone https://github.com/azrilaiman2003/augment-vip.git
cd augment-vip
```

2. 安装依赖（可选，用于彩色输出）：
```bash
pip install colorama
```

或者使用requirements.txt：
```bash
pip install -r requirements.txt
```

### 直接使用

无需安装，直接运行main.py即可：
```bash
python main.py --help
```

## 🔧 使用方法

### 清理VS Code数据库

删除VS Code数据库中的Augment相关条目：

```bash
python main.py clean
```

功能说明：
- 自动查找Windows系统下的VS Code数据库文件
- 创建数据库备份
- 删除包含"augment"的条目
- 显示操作结果

### 修改VS Code遥测ID

修改VS Code的storage.json文件中的遥测ID：

```bash
python main.py modify-ids
```

功能说明：
- 定位VS Code的storage.json文件
- 生成64字符的随机十六进制字符串作为machineId
- 生成随机UUID v4作为devDeviceId
- 创建原文件备份
- 更新文件中的新值

### 运行所有工具

同时运行数据库清理和ID修改：

```bash
python main.py all
```

### 查看帮助

```bash
python main.py --help
```

## 📁 项目结构

```
augment-vip/
├── main.py                 # 主程序文件（集成所有功能）
├── requirements.txt        # 依赖包列表
└── README.md              # 说明文档
```

## 🔍 工作原理

### 数据库清理工具

1. **查找数据库位置**: 自动检测Windows系统下VS Code数据库的正确路径
2. **创建备份**: 在进行任何更改之前，创建数据库文件的备份
3. **清理数据库**: 使用SQLite命令删除包含"augment"的条目
4. **报告结果**: 提供操作执行的详细反馈

### 遥测ID修改工具

1. **定位配置文件**: 查找VS Code的storage.json文件
2. **生成新ID**: 创建随机的machineId和devDeviceId
3. **安全更新**: 备份原文件后更新新的ID值
4. **确认更改**: 显示新生成的ID值

## 🛠️ 故障排除

### 常见问题

**Python未找到**
```
[ERROR] Python 3 is not installed or not in PATH
```
解决方案：安装Python 3.6或更高版本
- 从 https://www.python.org/downloads/ 下载并安装

**未找到数据库文件**
```
[WARNING] 未找到VS Code数据库
```
可能的原因：
- 您尚未在系统上使用过VS Code
- VS Code安装在非标准位置
- APPDATA环境变量未正确设置

**权限错误**
确保以管理员权限运行命令提示符或PowerShell

**colorama导入错误**
如果看到colorama相关错误，可以：
```bash
pip install colorama
```
或者程序会自动使用无颜色的输出模式

## 🤝 Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📜 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 📞 Contact

Azril Aiman - <EMAIL>

Project Link: [https://github.com/azrilaiman2003/augment-vip](https://github.com/azrilaiman2003/augment-vip)

---

Made with ❤️ by [Azril Aiman](https://github.com/azrilaiman2003)
