#!/usr/bin/env python3
"""
Augment VIP - Windows版本可视化工具
用于管理和清理VS Code数据库中与Augment相关的条目

功能:
- 清理VS Code数据库中的Augment相关条目
- 修改VS Code遥测ID以保护隐私
- 可视化GUI界面
- 仅支持Windows系统
"""

import os
import sys
import json
import sqlite3
import uuid
import shutil
import threading
from pathlib import Path
from typing import Dict, Optional

# 版本信息
__version__ = "2.0.0"

# GUI相关导入
try:
    import tkinter as tk
    from tkinter import ttk, messagebox, scrolledtext
    GUI_AVAILABLE = True
except ImportError:
    GUI_AVAILABLE = False

# 控制台颜色输出
try:
    from colorama import init, Fore, Style
    init()  # 初始化colorama以支持Windows

    def info(msg: str) -> None:
        """打印信息消息"""
        print(f"{Fore.BLUE}[INFO]{Style.RESET_ALL} {msg}")

    def success(msg: str) -> None:
        """打印成功消息"""
        print(f"{Fore.GREEN}[SUCCESS]{Style.RESET_ALL} {msg}")

    def warning(msg: str) -> None:
        """打印警告消息"""
        print(f"{Fore.YELLOW}[WARNING]{Style.RESET_ALL} {msg}")

    def error(msg: str) -> None:
        """打印错误消息"""
        print(f"{Fore.RED}[ERROR]{Style.RESET_ALL} {msg}")

except ImportError:
    # 如果colorama未安装，使用普通输出
    def info(msg: str) -> None:
        print(f"[INFO] {msg}")

    def success(msg: str) -> None:
        print(f"[SUCCESS] {msg}")

    def warning(msg: str) -> None:
        print(f"[WARNING] {msg}")

    def error(msg: str) -> None:
        print(f"[ERROR] {msg}")

def get_vscode_paths() -> Dict[str, Path]:
    """
    获取Windows系统下VS Code的路径

    Returns:
        包含VS Code目录和文件路径的字典
    """
    appdata = os.environ.get("APPDATA")
    if not appdata:
        error("未找到APPDATA环境变量")
        sys.exit(1)

    base_dir = Path(appdata) / "Code" / "User"

    paths = {
        "base_dir": base_dir,
        "storage_json": base_dir / "globalStorage" / "storage.json",
        "state_db": base_dir / "globalStorage" / "state.vscdb"
    }

    return paths

def backup_file(file_path: Path) -> Path:
    """
    创建文件备份

    Args:
        file_path: 要备份的文件路径

    Returns:
        备份文件路径
    """
    if not file_path.exists():
        error(f"文件不存在: {file_path}")
        sys.exit(1)

    backup_path = Path(f"{file_path}.backup")
    shutil.copy2(file_path, backup_path)
    success(f"已创建备份: {backup_path}")

    return backup_path

def generate_machine_id() -> str:
    """生成64字符的十六进制字符串作为machineId"""
    return uuid.uuid4().hex + uuid.uuid4().hex

def generate_device_id() -> str:
    """生成UUID v4作为devDeviceId"""
    return str(uuid.uuid4())

def clean_vscode_db() -> bool:
    """
    清理VS Code数据库，删除包含"augment"的条目

    Returns:
        成功返回True，失败返回False
    """
    info("开始数据库清理过程")

    # 获取VS Code路径
    paths = get_vscode_paths()
    state_db = paths["state_db"]

    if not state_db.exists():
        warning(f"未找到VS Code数据库: {state_db}")
        return False

    info(f"找到VS Code数据库: {state_db}")

    # 创建备份
    backup_path = backup_file(state_db)

    # 连接数据库
    try:
        conn = sqlite3.connect(str(state_db))
        cursor = conn.cursor()

        # 获取删除前的记录数
        cursor.execute("SELECT COUNT(*) FROM ItemTable WHERE key LIKE '%augment%'")
        count_before = cursor.fetchone()[0]

        if count_before == 0:
            info("数据库中未找到Augment相关条目")
            conn.close()
            return True

        # 删除包含"augment"的记录
        cursor.execute("DELETE FROM ItemTable WHERE key LIKE '%augment%'")
        conn.commit()

        # 获取删除后的记录数
        cursor.execute("SELECT COUNT(*) FROM ItemTable WHERE key LIKE '%augment%'")
        count_after = cursor.fetchone()[0]

        conn.close()

        success(f"已从数据库中删除 {count_before - count_after} 个Augment相关条目")
        return True

    except sqlite3.Error as e:
        error(f"SQLite错误: {e}")

        # 如果出错，从备份恢复
        if backup_path.exists():
            info("正在从备份恢复...")
            try:
                shutil.copy2(backup_path, state_db)
                success("已从备份恢复")
            except Exception as restore_error:
                error(f"从备份恢复失败: {restore_error}")

        return False
    except Exception as e:
        error(f"意外错误: {e}")
        return False

def modify_telemetry_ids() -> bool:
    """
    修改VS Code storage.json文件中的遥测ID

    Returns:
        成功返回True，失败返回False
    """
    info("开始VS Code遥测ID修改")

    # 获取VS Code路径
    paths = get_vscode_paths()
    storage_json = paths["storage_json"]

    if not storage_json.exists():
        warning(f"未找到VS Code storage.json: {storage_json}")
        return False

    info(f"找到storage.json: {storage_json}")

    # 创建备份
    backup_path = backup_file(storage_json)

    # 生成新ID
    info("正在生成新的遥测ID...")
    machine_id = generate_machine_id()
    device_id = generate_device_id()

    # 读取当前文件
    try:
        with open(storage_json, 'r', encoding='utf-8') as f:
            content = json.load(f)

        # 更新值
        content["telemetry.machineId"] = machine_id
        content["telemetry.devDeviceId"] = device_id

        # 写回文件
        with open(storage_json, 'w', encoding='utf-8') as f:
            json.dump(content, f, indent=2)

        success("成功更新遥测ID")
        info(f"新的machineId: {machine_id}")
        info(f"新的devDeviceId: {device_id}")
        info("您可能需要重启VS Code以使更改生效")

        return True

    except json.JSONDecodeError:
        error("存储文件不是有效的JSON格式")
        return False
    except Exception as e:
        error(f"意外错误: {e}")
        return False

class AugmentVIPGUI:
    """Augment VIP 可视化界面"""

    def __init__(self):
        self.root = tk.Tk()
        self.root.title(f"Augment VIP v{__version__} - Windows版本")
        self.root.geometry("600x500")
        self.root.resizable(False, False)

        # 设置图标和样式
        self.setup_styles()
        self.create_widgets()

    def setup_styles(self):
        """设置界面样式"""
        style = ttk.Style()
        style.theme_use('clam')

        # 配置按钮样式
        style.configure('Action.TButton', font=('微软雅黑', 10), padding=10)
        style.configure('Title.TLabel', font=('微软雅黑', 16, 'bold'))
        style.configure('Info.TLabel', font=('微软雅黑', 9))

    def create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 标题
        title_label = ttk.Label(main_frame, text="Augment VIP", style='Title.TLabel')
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 10))

        subtitle_label = ttk.Label(main_frame, text="VS Code 数据库管理工具", style='Info.TLabel')
        subtitle_label.grid(row=1, column=0, columnspan=2, pady=(0, 20))

        # 功能按钮区域
        button_frame = ttk.LabelFrame(main_frame, text="功能选择", padding="15")
        button_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 15))

        # 清理数据库按钮
        clean_btn = ttk.Button(button_frame, text="清理VS Code数据库",
                              command=self.clean_database, style='Action.TButton')
        clean_btn.grid(row=0, column=0, padx=5, pady=5, sticky=(tk.W, tk.E))

        # 修改ID按钮
        modify_btn = ttk.Button(button_frame, text="修改遥测ID",
                               command=self.modify_ids, style='Action.TButton')
        modify_btn.grid(row=0, column=1, padx=5, pady=5, sticky=(tk.W, tk.E))

        # 运行所有按钮
        all_btn = ttk.Button(button_frame, text="运行所有功能",
                            command=self.run_all, style='Action.TButton')
        all_btn.grid(row=1, column=0, columnspan=2, padx=5, pady=5, sticky=(tk.W, tk.E))

        # 配置按钮列权重
        button_frame.columnconfigure(0, weight=1)
        button_frame.columnconfigure(1, weight=1)

        # 日志输出区域
        log_frame = ttk.LabelFrame(main_frame, text="操作日志", padding="10")
        log_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 15))

        # 日志文本框
        self.log_text = scrolledtext.ScrolledText(log_frame, height=12, width=70,
                                                 font=('Consolas', 9), wrap=tk.WORD)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 底部按钮
        bottom_frame = ttk.Frame(main_frame)
        bottom_frame.grid(row=4, column=0, columnspan=2, sticky=(tk.W, tk.E))

        # 清空日志按钮
        clear_btn = ttk.Button(bottom_frame, text="清空日志", command=self.clear_log)
        clear_btn.grid(row=0, column=0, padx=(0, 10))

        # 关于按钮
        about_btn = ttk.Button(bottom_frame, text="关于", command=self.show_about)
        about_btn.grid(row=0, column=1, padx=(0, 10))

        # 退出按钮
        exit_btn = ttk.Button(bottom_frame, text="退出", command=self.root.quit)
        exit_btn.grid(row=0, column=2)

        # 配置权重
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(3, weight=1)
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)

        # 初始化日志
        self.log("欢迎使用 Augment VIP v" + __version__)
        self.log("请选择要执行的操作")

    def log(self, message, level="INFO"):
        """添加日志消息"""
        import datetime
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        log_message = f"[{timestamp}] [{level}] {message}\n"

        self.log_text.insert(tk.END, log_message)
        self.log_text.see(tk.END)
        self.root.update()

    def clear_log(self):
        """清空日志"""
        self.log_text.delete(1.0, tk.END)

    def show_about(self):
        """显示关于对话框"""
        about_text = f"""Augment VIP v{__version__}

VS Code 数据库管理工具

功能:
• 清理VS Code数据库中的Augment相关条目
• 修改VS Code遥测ID以保护隐私
• 自动备份重要文件

注意事项:
• 使用前请关闭VS Code
• 建议以管理员权限运行
• 所有操作都会自动创建备份

开发: Augment VIP Team"""

        messagebox.showinfo("关于 Augment VIP", about_text)

    def run_in_thread(self, func, *args):
        """在后台线程中运行函数"""
        def wrapper():
            try:
                func(*args)
            except Exception as e:
                self.log(f"操作失败: {e}", "ERROR")
                messagebox.showerror("错误", f"操作失败: {e}")

        thread = threading.Thread(target=wrapper)
        thread.daemon = True
        thread.start()

    def clean_database(self):
        """清理数据库"""
        self.log("开始清理VS Code数据库...")
        self.run_in_thread(self._clean_database)

    def _clean_database(self):
        """实际的数据库清理操作"""
        if clean_vscode_db_gui(self.log):
            self.log("数据库清理完成", "SUCCESS")
            messagebox.showinfo("成功", "数据库清理完成！")
        else:
            self.log("数据库清理失败", "ERROR")
            messagebox.showerror("失败", "数据库清理失败！")

    def modify_ids(self):
        """修改遥测ID"""
        self.log("开始修改VS Code遥测ID...")
        self.run_in_thread(self._modify_ids)

    def _modify_ids(self):
        """实际的ID修改操作"""
        if modify_telemetry_ids_gui(self.log):
            self.log("遥测ID修改完成", "SUCCESS")
            messagebox.showinfo("成功", "遥测ID修改完成！")
        else:
            self.log("遥测ID修改失败", "ERROR")
            messagebox.showerror("失败", "遥测ID修改失败！")

    def run_all(self):
        """运行所有功能"""
        self.log("开始运行所有功能...")
        self.run_in_thread(self._run_all)

    def _run_all(self):
        """实际的运行所有功能操作"""
        clean_result = clean_vscode_db_gui(self.log)
        modify_result = modify_telemetry_ids_gui(self.log)

        if clean_result and modify_result:
            self.log("所有操作完成", "SUCCESS")
            messagebox.showinfo("成功", "所有操作完成！")
        else:
            self.log("部分操作失败", "ERROR")
            messagebox.showerror("失败", "部分操作失败！")

    def run(self):
        """运行GUI"""
        self.root.mainloop()

# GUI版本的功能函数
def clean_vscode_db_gui(log_func):
    """GUI版本的数据库清理函数"""
    log_func("开始数据库清理过程")

    # 获取VS Code路径
    paths = get_vscode_paths()
    state_db = paths["state_db"]

    if not state_db.exists():
        log_func(f"未找到VS Code数据库: {state_db}", "WARNING")
        return False

    log_func(f"找到VS Code数据库: {state_db}")

    # 创建备份
    backup_path = backup_file_gui(state_db, log_func)

    # 连接数据库
    try:
        conn = sqlite3.connect(str(state_db))
        cursor = conn.cursor()

        # 获取删除前的记录数
        cursor.execute("SELECT COUNT(*) FROM ItemTable WHERE key LIKE '%augment%'")
        count_before = cursor.fetchone()[0]

        if count_before == 0:
            log_func("数据库中未找到Augment相关条目")
            conn.close()
            return True

        # 删除包含"augment"的记录
        cursor.execute("DELETE FROM ItemTable WHERE key LIKE '%augment%'")
        conn.commit()

        # 获取删除后的记录数
        cursor.execute("SELECT COUNT(*) FROM ItemTable WHERE key LIKE '%augment%'")
        count_after = cursor.fetchone()[0]

        conn.close()

        log_func(f"已从数据库中删除 {count_before - count_after} 个Augment相关条目", "SUCCESS")
        return True

    except sqlite3.Error as e:
        log_func(f"SQLite错误: {e}", "ERROR")

        # 如果出错，从备份恢复
        if backup_path.exists():
            log_func("正在从备份恢复...")
            try:
                shutil.copy2(backup_path, state_db)
                log_func("已从备份恢复", "SUCCESS")
            except Exception as restore_error:
                log_func(f"从备份恢复失败: {restore_error}", "ERROR")

        return False
    except Exception as e:
        log_func(f"意外错误: {e}", "ERROR")
        return False

def modify_telemetry_ids_gui(log_func):
    """GUI版本的遥测ID修改函数"""
    log_func("开始VS Code遥测ID修改")

    # 获取VS Code路径
    paths = get_vscode_paths()
    storage_json = paths["storage_json"]

    if not storage_json.exists():
        log_func(f"未找到VS Code storage.json: {storage_json}", "WARNING")
        return False

    log_func(f"找到storage.json: {storage_json}")

    # 创建备份
    backup_path = backup_file_gui(storage_json, log_func)

    # 生成新ID
    log_func("正在生成新的遥测ID...")
    machine_id = generate_machine_id()
    device_id = generate_device_id()

    # 读取当前文件
    try:
        with open(storage_json, 'r', encoding='utf-8') as f:
            content = json.load(f)

        # 更新值
        content["telemetry.machineId"] = machine_id
        content["telemetry.devDeviceId"] = device_id

        # 写回文件
        with open(storage_json, 'w', encoding='utf-8') as f:
            json.dump(content, f, indent=2)

        log_func("成功更新遥测ID", "SUCCESS")
        log_func(f"新的machineId: {machine_id}")
        log_func(f"新的devDeviceId: {device_id}")
        log_func("您可能需要重启VS Code以使更改生效")

        return True

    except json.JSONDecodeError:
        log_func("存储文件不是有效的JSON格式", "ERROR")
        return False
    except Exception as e:
        log_func(f"意外错误: {e}", "ERROR")
        return False

def backup_file_gui(file_path: Path, log_func) -> Path:
    """GUI版本的文件备份函数"""
    if not file_path.exists():
        log_func(f"文件不存在: {file_path}", "ERROR")
        raise FileNotFoundError(f"文件不存在: {file_path}")

    backup_path = Path(f"{file_path}.backup")
    shutil.copy2(file_path, backup_path)
    log_func(f"已创建备份: {backup_path}", "SUCCESS")

    return backup_path

def main():
    """主函数"""
    if GUI_AVAILABLE:
        # 启动GUI界面
        app = AugmentVIPGUI()
        app.run()
    else:
        # 如果GUI不可用，显示错误信息
        print("错误: 无法启动GUI界面")
        print("请确保已安装tkinter库")
        sys.exit(1)

if __name__ == "__main__":
    main()