#!/usr/bin/env python3
"""
Augment VIP - Windows版本可视化工具
用于管理和清理VS Code数据库中与Augment相关的条目

功能:
- 清理VS Code数据库中的Augment相关条目
- 修改VS Code遥测ID以保护隐私
- 可视化GUI界面
- 仅支持Windows系统
"""

import os
import sys
import json
import sqlite3
import uuid
import shutil
import threading
import queue
import time
import re
import base64
import random
import string
import subprocess
import winreg
from pathlib import Path
from typing import Dict
from datetime import datetime

# 邮箱相关导入
try:
    import poplib
    import email
    from bs4 import BeautifulSoup
    EMAIL_AVAILABLE = True
except ImportError:
    EMAIL_AVAILABLE = False

# 版本信息
__version__ = "2.0.0"

# 邮箱配置
POP3_SERVER = 'pop.2925.com'
POP3_PORT = 110
REFRESH_INTERVAL_SECONDS = 1

# GUI相关导入
try:
    import tkinter as tk
    from tkinter import ttk, messagebox, scrolledtext
    GUI_AVAILABLE = True
except ImportError:
    GUI_AVAILABLE = False

# 控制台颜色输出
try:
    from colorama import init, Fore, Style
    init()  # 初始化colorama以支持Windows
    
    def info(msg: str) -> None:
        """打印信息消息"""
        print(f"{Fore.BLUE}[INFO]{Style.RESET_ALL} {msg}")
    
    def success(msg: str) -> None:
        """打印成功消息"""
        print(f"{Fore.GREEN}[SUCCESS]{Style.RESET_ALL} {msg}")
    
    def warning(msg: str) -> None:
        """打印警告消息"""
        print(f"{Fore.YELLOW}[WARNING]{Style.RESET_ALL} {msg}")
    
    def error(msg: str) -> None:
        """打印错误消息"""
        print(f"{Fore.RED}[ERROR]{Style.RESET_ALL} {msg}")
        
except ImportError:
    # 如果colorama未安装，使用普通输出
    def info(msg: str) -> None:
        print(f"[INFO] {msg}")
    
    def success(msg: str) -> None:
        print(f"[SUCCESS] {msg}")
    
    def warning(msg: str) -> None:
        print(f"[WARNING] {msg}")
    
    def error(msg: str) -> None:
        print(f"[ERROR] {msg}")

def get_vscode_paths() -> Dict[str, Path]:
    """
    获取Windows系统下VS Code的路径
    
    Returns:
        包含VS Code目录和文件路径的字典
    """
    appdata = os.environ.get("APPDATA")
    if not appdata:
        error("未找到APPDATA环境变量")
        sys.exit(1)
    
    base_dir = Path(appdata) / "Code" / "User"
    
    paths = {
        "base_dir": base_dir,
        "storage_json": base_dir / "globalStorage" / "storage.json",
        "state_db": base_dir / "globalStorage" / "state.vscdb"
    }
    
    return paths

def backup_file(file_path: Path) -> Path:
    """
    创建文件备份
    
    Args:
        file_path: 要备份的文件路径
        
    Returns:
        备份文件路径
    """
    if not file_path.exists():
        error(f"文件不存在: {file_path}")
        sys.exit(1)
        
    backup_path = Path(f"{file_path}.backup")
    shutil.copy2(file_path, backup_path)
    success(f"已创建备份: {backup_path}")
    
    return backup_path

def generate_machine_id() -> str:
    """生成64字符的十六进制字符串作为machineId"""
    return uuid.uuid4().hex + uuid.uuid4().hex

def generate_device_id() -> str:
    """生成UUID v4作为devDeviceId"""
    return str(uuid.uuid4())

def clean_vscode_db() -> bool:
    """
    清理VS Code数据库，删除包含"augment"的条目
    
    Returns:
        成功返回True，失败返回False
    """
    info("开始数据库清理过程")
    
    # 获取VS Code路径
    paths = get_vscode_paths()
    state_db = paths["state_db"]
    
    if not state_db.exists():
        warning(f"未找到VS Code数据库: {state_db}")
        return False
    
    info(f"找到VS Code数据库: {state_db}")
    
    # 创建备份
    backup_path = backup_file(state_db)
    
    # 连接数据库
    try:
        conn = sqlite3.connect(str(state_db))
        cursor = conn.cursor()
        
        # 获取删除前的记录数
        cursor.execute("SELECT COUNT(*) FROM ItemTable WHERE key LIKE '%augment%'")
        count_before = cursor.fetchone()[0]
        
        if count_before == 0:
            info("数据库中未找到Augment相关条目")
            conn.close()
            return True
        
        # 删除包含"augment"的记录
        cursor.execute("DELETE FROM ItemTable WHERE key LIKE '%augment%'")
        conn.commit()
        
        # 获取删除后的记录数
        cursor.execute("SELECT COUNT(*) FROM ItemTable WHERE key LIKE '%augment%'")
        count_after = cursor.fetchone()[0]
        
        conn.close()
        
        success(f"已从数据库中删除 {count_before - count_after} 个Augment相关条目")
        return True
        
    except sqlite3.Error as e:
        error(f"SQLite错误: {e}")
        
        # 如果出错，从备份恢复
        if backup_path.exists():
            info("正在从备份恢复...")
            try:
                shutil.copy2(backup_path, state_db)
                success("已从备份恢复")
            except Exception as restore_error:
                error(f"从备份恢复失败: {restore_error}")
        
        return False
    except Exception as e:
        error(f"意外错误: {e}")
        return False

def modify_telemetry_ids() -> bool:
    """
    修改VS Code storage.json文件中的遥测ID
    
    Returns:
        成功返回True，失败返回False
    """
    info("开始VS Code遥测ID修改")
    
    # 获取VS Code路径
    paths = get_vscode_paths()
    storage_json = paths["storage_json"]
    
    if not storage_json.exists():
        warning(f"未找到VS Code storage.json: {storage_json}")
        return False
    
    info(f"找到storage.json: {storage_json}")
    
    # 创建备份
    backup_path = backup_file(storage_json)
    
    # 生成新ID
    info("正在生成新的遥测ID...")
    machine_id = generate_machine_id()
    device_id = generate_device_id()
    
    # 读取当前文件
    try:
        with open(storage_json, 'r', encoding='utf-8') as f:
            content = json.load(f)
        
        # 更新值
        content["telemetry.machineId"] = machine_id
        content["telemetry.devDeviceId"] = device_id
        
        # 写回文件
        with open(storage_json, 'w', encoding='utf-8') as f:
            json.dump(content, f, indent=2)
        
        success("成功更新遥测ID")
        info(f"新的machineId: {machine_id}")
        info(f"新的devDeviceId: {device_id}")
        info("您可能需要重启VS Code以使更改生效")
        
        return True
        
    except json.JSONDecodeError:
        error("存储文件不是有效的JSON格式")
        return False
    except Exception as e:
        error(f"意外错误: {e}")
        return False

class AugmentVIPGUI:
    """Augment VIP 可视化界面"""

    def __init__(self):
        self.root = tk.Tk()
        self.root.title(f"Augment VIP v{__version__}")

        # 设置主窗口大小和位置
        self.root.geometry("500x400")
        self.root.minsize(400, 700)  # 设置最小尺寸
        self.root.resizable(True, True)  # 允许调整大小

        # 设置图标和样式
        self.setup_styles()
        self.create_widgets()

    def setup_styles(self):
        """设置界面样式"""
        style = ttk.Style()
        style.theme_use('clam')

        # 配置按钮样式
        style.configure('Reset.TButton', font=('微软雅黑', 14, 'bold'), padding=20)
        style.configure('Title.TLabel', font=('微软雅黑', 18, 'bold'))
        style.configure('Info.TLabel', font=('微软雅黑', 10))

    def create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="30")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 标题
        title_label = ttk.Label(main_frame, text="Augment VIP", style='Title.TLabel')
        title_label.grid(row=0, column=0, pady=(0, 10))

        subtitle_label = ttk.Label(main_frame, text="VS Code 一键重置工具", style='Info.TLabel')
        subtitle_label.grid(row=1, column=0, pady=(0, 30))

        # 功能说明
        info_text = """此工具将执行以下操作：
• 清理VS Code数据库中的Augment相关条目
• 生成新的随机遥测ID
• 自动备份所有修改的文件

注意：使用前请确保VS Code已完全关闭"""

        info_label = ttk.Label(main_frame, text=info_text, style='Info.TLabel', justify='left')
        info_label.grid(row=2, column=0, pady=(0, 30))

        # 按钮区域框架
        button_area_frame = ttk.Frame(main_frame)
        button_area_frame.grid(row=3, column=0, pady=(0, 20), sticky=(tk.W, tk.E))
        button_area_frame.columnconfigure(0, weight=1)
        button_area_frame.columnconfigure(1, weight=1)

        # 一键重置按钮
        reset_btn = ttk.Button(button_area_frame, text="一键重置",
                              command=self.one_click_reset, style='Reset.TButton')
        reset_btn.grid(row=0, column=0, padx=(0, 10), sticky=(tk.W, tk.E))

        # 邮箱接收按钮
        email_btn = ttk.Button(button_area_frame, text="邮箱接收",
                              command=self.open_email_tool, style='Reset.TButton')
        email_btn.grid(row=0, column=1, padx=(10, 0), sticky=(tk.W, tk.E))

        # 日志输出区域
        log_frame = ttk.LabelFrame(main_frame, text="操作日志", padding="10")
        log_frame.grid(row=4, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 15))

        # 日志文本框
        self.log_text = scrolledtext.ScrolledText(log_frame, height=8, width=60,
                                                 font=('Consolas', 9), wrap=tk.WORD)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 底部按钮
        bottom_frame = ttk.Frame(main_frame)
        bottom_frame.grid(row=5, column=0, sticky=(tk.W, tk.E), pady=(10, 0))

        # 清空日志按钮
        clear_btn = ttk.Button(bottom_frame, text="清空日志", command=self.clear_log)
        clear_btn.grid(row=0, column=0, padx=(0, 10))

        # 退出按钮
        exit_btn = ttk.Button(bottom_frame, text="退出", command=self.root.quit)
        exit_btn.grid(row=0, column=1)

        # 配置权重，使组件能够随窗口大小调整
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(4, weight=1)  # 日志区域可以扩展
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)

        # 初始化日志
        self.log("欢迎使用 Augment VIP v" + __version__)
        self.log("点击'一键重置'开始操作")

    def log(self, message, level="INFO"):
        """添加日志消息"""
        import datetime
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        log_message = f"[{timestamp}] [{level}] {message}\n"

        self.log_text.insert(tk.END, log_message)
        self.log_text.see(tk.END)
        self.root.update()

    def clear_log(self):
        """清空日志"""
        self.log_text.delete(1.0, tk.END)

    def run_in_thread(self, func, *args):
        """在后台线程中运行函数"""
        def wrapper():
            try:
                func(*args)
            except Exception as e:
                self.log(f"操作失败: {e}", "ERROR")
                messagebox.showerror("错误", f"操作失败: {e}")

        thread = threading.Thread(target=wrapper)
        thread.daemon = True
        thread.start()

    def one_click_reset(self):
        """一键重置功能"""
        # 确认对话框
        result = messagebox.askyesno(
            "确认操作",
            "此操作将：\n\n• 清理VS Code数据库中的Augment相关条目\n• 生成新的随机遥测ID\n• 自动备份所有修改的文件\n\n确定要继续吗？",
            icon='warning'
        )

        if result:
            self.log("开始一键重置操作...")
            self.run_in_thread(self._one_click_reset)

    def _one_click_reset(self):
        """实际的一键重置操作"""
        success_count = 0
        total_operations = 2

        # 执行数据库清理
        self.log("正在清理VS Code数据库...")
        if clean_vscode_db_gui(self.log):
            success_count += 1

        # 执行遥测ID修改
        self.log("正在修改遥测ID...")
        if modify_telemetry_ids_gui(self.log):
            success_count += 1

        # 显示结果
        if success_count == total_operations:
            self.log("一键重置完成！所有操作成功执行", "SUCCESS")
            messagebox.showinfo("操作完成", "一键重置完成！\n\n请重启VS Code以使更改生效。")
        elif success_count > 0:
            self.log(f"部分操作完成 ({success_count}/{total_operations})", "WARNING")
            messagebox.showwarning("部分完成", f"部分操作完成 ({success_count}/{total_operations})\n\n请检查日志了解详情。")
        else:
            self.log("一键重置失败", "ERROR")
            messagebox.showerror("操作失败", "一键重置失败！\n\n请检查日志了解详情。")

    def open_email_tool(self):
        """打开邮箱接收工具"""
        try:
            # 直接在新窗口中启动邮箱工具，不依赖外部进程
            self.log("正在启动邮箱接收工具...", "INFO")

            # 创建新的顶级窗口
            email_window = tk.Toplevel(self.root)
            email_window.title("📧 邮箱验证码监控器")

            # 设置窗口大小和位置
            email_window.geometry("700x830")
            email_window.resizable(True, True)

            # 设置最小窗口大小
            email_window.minsize(600, 700)

            # 直接创建内联邮箱工具
            self._create_email_monitor_gui(email_window)

            self.log("邮箱接收工具已启动", "SUCCESS")

        except ImportError:
            # 如果无法导入，尝试内联实现
            self.log("正在加载邮箱接收工具...", "INFO")
            self._create_email_monitor_gui(email_window)
        except Exception as e:
            self.log(f"启动邮箱接收工具失败: {e}", "ERROR")
            messagebox.showerror("错误", f"启动邮箱接收工具失败: {e}")

    def run(self):
        """运行GUI"""
        self.root.mainloop()

    def _create_email_monitor_gui(self, parent_window):
        """创建完整的邮箱监控GUI"""
        try:
            # 使用传入的窗口
            email_window = parent_window

            # 创建邮箱监控实例
            email_monitor = EmailMonitor(email_window)

            self.log("邮箱接收工具已启动", "SUCCESS")

        except Exception as e:
            self.log(f"创建邮箱工具失败: {e}", "ERROR")
            messagebox.showerror("错误", f"创建邮箱工具失败: {e}")

class EmailMonitor:
    """完整的邮箱监控GUI类"""

    def __init__(self, root):
        self.root = root
        self.root.title("📧 邮箱验证码监控器")

        # 监控状态
        self.monitoring = False
        self.monitor_thread = None
        self.output_queue = queue.Queue()

        # 配置管理 - 使用Windows注册表
        self.registry_key = r"SOFTWARE\AugmentVIP\EmailMonitor"

        self.setup_ui()
        self.load_config()
        self.check_queue()
        self.start_random_account_refresh()

    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(6, weight=1)

        # 标题区域
        title_frame = ttk.Frame(main_frame)
        title_frame.grid(row=0, column=0, columnspan=2, pady=(0, 20), sticky=(tk.W, tk.E))
        title_frame.columnconfigure(0, weight=1)

        title_label = ttk.Label(title_frame, text="📧 邮箱验证码监控器",
                               font=('微软雅黑', 16, 'bold'))
        title_label.grid(row=0, column=0)

        subtitle_label = ttk.Label(title_frame, text="自动监控邮箱并提取验证码",
                                  font=('微软雅黑', 10))
        subtitle_label.grid(row=1, column=0, pady=(5, 0))

        # 输入区域框架
        input_frame = ttk.LabelFrame(main_frame, text="📝 登录信息", padding="15")
        input_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 15))
        input_frame.columnconfigure(1, weight=1)

        # 邮箱账号输入
        ttk.Label(input_frame, text="📧 邮箱账号:", font=('微软雅黑', 10)).grid(
            row=0, column=0, sticky=tk.W, pady=8)
        self.email_var = tk.StringVar()
        self.email_entry = ttk.Entry(input_frame, textvariable=self.email_var, width=35)
        self.email_entry.grid(row=0, column=1, columnspan=3, sticky=(tk.W, tk.E), pady=8, padx=(15, 0))

        # 随机账户显示区域
        ttk.Label(input_frame, text="🎲 随机账户:", font=('微软雅黑', 10)).grid(
            row=1, column=0, sticky=tk.W, pady=8)
        self.random_account_var = tk.StringVar()
        self.random_account_entry = ttk.Entry(
            input_frame, textvariable=self.random_account_var, width=35, state='readonly'
        )
        self.random_account_entry.grid(row=1, column=1, sticky=(tk.W, tk.E), pady=8, padx=(15, 5))

        # 复制随机账户按钮
        self.copy_random_button = ttk.Button(input_frame, text="📋 复制",
                                           command=self.copy_random_account)
        self.copy_random_button.grid(row=1, column=2, padx=(5, 0), pady=8, ipadx=10)

        # 邮箱密码输入
        ttk.Label(input_frame, text="🔒 邮箱密码:", font=('微软雅黑', 10)).grid(
            row=2, column=0, sticky=tk.W, pady=8)
        self.password_var = tk.StringVar()
        self.password_entry = ttk.Entry(input_frame, textvariable=self.password_var,
                                       show="*", width=35)
        self.password_entry.grid(row=2, column=1, columnspan=3, sticky=(tk.W, tk.E),
                                pady=8, padx=(15, 0))

        # 选项框架
        options_frame = ttk.Frame(input_frame)
        options_frame.grid(row=3, column=0, columnspan=4, pady=(10, 0), sticky=tk.W)

        # 记住密码选项 - 使用自定义显示
        self.remember_password_var = tk.BooleanVar(value=True)
        remember_frame = ttk.Frame(options_frame)
        remember_frame.pack(side=tk.LEFT, padx=(0, 20))

        self.remember_password_check = ttk.Checkbutton(remember_frame,
                                                      variable=self.remember_password_var,
                                                      command=self.update_checkbox_display)
        self.remember_password_check.pack(side=tk.LEFT)

        self.remember_label = ttk.Label(remember_frame, text="🔐 记住账号密码",
                                       font=('微软雅黑', 9))
        self.remember_label.pack(side=tk.LEFT, padx=(5, 0))

        # 自动复制选项 - 使用自定义显示
        self.auto_copy_var = tk.BooleanVar(value=True)
        copy_frame = ttk.Frame(options_frame)
        copy_frame.pack(side=tk.LEFT)

        self.auto_copy_check = ttk.Checkbutton(copy_frame,
                                              variable=self.auto_copy_var,
                                              command=self.update_checkbox_display)
        self.auto_copy_check.pack(side=tk.LEFT)

        self.copy_label = ttk.Label(copy_frame, text="📋 自动复制验证码",
                                   font=('微软雅黑', 9))
        self.copy_label.pack(side=tk.LEFT, padx=(5, 0))

        # 初始化复选框显示
        self.update_checkbox_display()

        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=2, column=0, columnspan=2, pady=15)

        # 开始/停止按钮
        self.start_button = ttk.Button(button_frame, text="🚀 开始监控",
                                      command=self.toggle_monitoring)
        self.start_button.pack(side=tk.LEFT, padx=10, ipadx=20, ipady=5)

        # 清空日志按钮
        self.clear_button = ttk.Button(button_frame, text="🗑️ 清空日志",
                                      command=self.clear_log)
        self.clear_button.pack(side=tk.LEFT, padx=10, ipadx=20, ipady=5)

        # 打开网站按钮
        self.website_button = ttk.Button(button_frame, text="🌐 打开网站",
                                        command=self.open_website)
        self.website_button.pack(side=tk.LEFT, padx=10, ipadx=20, ipady=5)

        # 状态区域
        status_frame = ttk.Frame(main_frame)
        status_frame.grid(row=3, column=0, columnspan=2, pady=10, sticky=(tk.W, tk.E))
        status_frame.columnconfigure(0, weight=1)

        # 状态标签
        self.status_var = tk.StringVar(value="🟢 就绪")
        self.status_label = ttk.Label(status_frame, textvariable=self.status_var,
                                     font=('微软雅黑', 9), anchor='center')
        self.status_label.grid(row=0, column=0, sticky=(tk.W, tk.E))

        # 分隔线
        separator = ttk.Separator(main_frame, orient='horizontal')
        separator.grid(row=4, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=10)

        # 日志显示区域
        log_frame = ttk.LabelFrame(main_frame, text="📋 监控日志", padding="10")
        log_frame.grid(row=5, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)

        self.log_text = scrolledtext.ScrolledText(
            log_frame, wrap=tk.WORD, height=12, font=('Consolas', 9),
            bg='#f8f9fa', fg='#2c3e50'
        )
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 验证码显示区域
        result_frame = ttk.LabelFrame(main_frame, text="🎯 验证码结果", padding="15")
        result_frame.grid(row=6, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        result_frame.columnconfigure(1, weight=1)

        ttk.Label(result_frame, text="🔢 验证码:", font=('微软雅黑', 11, 'bold')).grid(
            row=0, column=0, sticky=tk.W, pady=5)
        self.code_var = tk.StringVar()
        self.code_entry = ttk.Entry(
            result_frame, textvariable=self.code_var, font=("Consolas", 16, "bold"),
            justify='center', state='readonly'
        )
        self.code_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(15, 10),
                            pady=5, ipady=8)

        # 复制按钮
        self.copy_button = ttk.Button(result_frame, text="📋 复制", command=self.copy_code)
        self.copy_button.grid(row=0, column=2, padx=(0, 0), pady=5, ipadx=15, ipady=8)

    def update_checkbox_display(self):
        """更新复选框显示，使用对号而不是叉号"""
        # 更新记住密码复选框显示
        if self.remember_password_var.get():
            self.remember_label.config(text="✅ 记住账号密码")
        else:
            self.remember_label.config(text="🔐 记住账号密码")

        # 更新自动复制复选框显示
        if self.auto_copy_var.get():
            self.copy_label.config(text="✅ 自动复制验证码")
        else:
            self.copy_label.config(text="📋 自动复制验证码")

    def log_message(self, message):
        """在日志区域添加消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {message}\n"

        self.log_text.insert(tk.END, formatted_message)
        self.log_text.see(tk.END)

        # 如果是验证码消息，提取并显示
        if message.startswith("VERIFICATION_CODE:"):
            code = message.split(":", 1)[1]
            self.code_var.set(code)

            # 自动复制到剪贴板
            if self.auto_copy_var.get():
                try:
                    self.root.clipboard_clear()
                    self.root.clipboard_append(code)
                    self.status_var.set(f"✅ 验证码已获取并复制到剪贴板: {code}")
                    copy_timestamp = datetime.now().strftime("%H:%M:%S")
                    copy_message = f"[{copy_timestamp}] ✅ 验证码已自动复制到剪贴板\n"
                    self.log_text.insert(tk.END, copy_message)
                    self.log_text.see(tk.END)
                except Exception as e:
                    self.status_var.set(f"⚠️ 验证码已获取: {code} (复制失败)")
            else:
                self.status_var.set(f"✅ 验证码已获取: {code}")

            # 自动停止监控
            self.stop_monitoring()

    def clear_log(self):
        """清空日志"""
        self.log_text.delete(1.0, tk.END)
        self.code_var.set("")

    def copy_code(self):
        """复制验证码到剪贴板"""
        code = self.code_var.get()
        if code:
            self.root.clipboard_clear()
            self.root.clipboard_append(code)
            messagebox.showinfo("提示", "验证码已复制到剪贴板！")
        else:
            messagebox.showwarning("警告", "没有验证码可复制！")

    def open_website(self):
        """在谷歌浏览器无痕模式下打开网站"""
        try:
            import subprocess
            import os

            url = "https://www.augmentcode.com/"

            # 尝试不同的Chrome路径
            chrome_paths = [
                r"C:\Program Files\Google\Chrome\Application\chrome.exe",
                r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
                r"C:\Users\<USER>\AppData\Local\Google\Chrome\Application\chrome.exe".format(os.getenv('USERNAME')),
                "chrome"  # 如果Chrome在PATH中
            ]

            chrome_found = False
            for chrome_path in chrome_paths:
                try:
                    if chrome_path == "chrome" or os.path.exists(chrome_path):
                        # 使用无痕模式打开网站
                        subprocess.Popen([chrome_path, "--incognito", url])
                        chrome_found = True
                        self.log_message("已在Chrome无痕模式下打开 Augment Code 网站")
                        break
                except:
                    continue

            if not chrome_found:
                # 如果找不到Chrome，使用默认浏览器
                import webbrowser
                webbrowser.open(url)
                self.log_message("已在默认浏览器中打开 Augment Code 网站")

        except Exception as e:
            self.log_message(f"打开网站失败: {e}")
            messagebox.showerror("错误", f"打开网站失败: {e}")

    def generate_random_account(self):
        """生成随机邮箱账户"""
        current_account = self.email_var.get().strip()

        if not current_account:
            base_account = "user"
        else:
            if "@" in current_account:
                base_account = current_account.split("@")[0]
            else:
                base_account = current_account

        random_letters = ''.join(random.choices(string.ascii_lowercase, k=6))
        random_account = f"{base_account}{random_letters}@2925.com"
        self.random_account_var.set(random_account)
        return random_account

    def copy_random_account(self):
        """复制随机邮箱账户到剪贴板"""
        account = self.random_account_var.get().strip()
        if account:
            self.root.clipboard_clear()
            self.root.clipboard_append(account)
            messagebox.showinfo("提示", "随机邮箱账户已复制到剪贴板！")
        else:
            messagebox.showwarning("警告", "没有随机邮箱账户可复制！")

    def start_random_account_refresh(self):
        """启动随机账户自动刷新"""
        self.refresh_random_account()

    def refresh_random_account(self):
        """刷新随机账户并安排下次刷新"""
        self.generate_random_account()
        self.root.after(1000, self.refresh_random_account)

    def toggle_monitoring(self):
        """切换监控状态"""
        if not self.monitoring:
            self.start_monitoring()
        else:
            self.stop_monitoring()

    def start_monitoring(self):
        """开始监控"""
        if not EMAIL_AVAILABLE:
            messagebox.showerror("错误", "缺少邮箱相关依赖包！\n请安装: pip install poplib beautifulsoup4")
            return

        email_account = self.email_var.get().strip()
        email_password = self.password_var.get().strip()

        if not email_account or not email_password:
            messagebox.showerror("错误", "请输入邮箱账号和密码！")
            return

        # 保存配置
        self.save_config()

        self.monitoring = True
        self.start_button.config(text="⏹️ 停止监控", state="normal")
        self.email_entry.config(state="disabled")
        self.password_entry.config(state="disabled")
        self.auto_copy_check.config(state="disabled")
        self.remember_password_check.config(state="disabled")
        self.status_var.set("🔄 正在启动监控...")
        self.code_var.set("")

        # 在新线程中运行监控
        self.monitor_thread = threading.Thread(
            target=email_monitor_main,
            args=(email_account, email_password, self.output_queue),
            daemon=True
        )
        self.monitor_thread.start()

    def stop_monitoring(self):
        """停止监控"""
        if self.monitoring:
            self.monitoring = False
            self.output_queue.put('STOP')

            # 保存配置
            self.save_config()

            self.start_button.config(text="🚀 开始监控")
            self.email_entry.config(state="normal")
            self.password_entry.config(state="normal")
            self.auto_copy_check.config(state="normal")
            self.remember_password_check.config(state="normal")
            self.status_var.set("🔴 监控已停止")

    def check_queue(self):
        """检查队列中的消息"""
        try:
            while True:
                message = self.output_queue.get_nowait()
                if message == 'STOP':
                    break
                self.log_message(message)
        except queue.Empty:
            pass

        # 每100毫秒检查一次队列
        self.root.after(100, self.check_queue)

    def load_config(self):
        """从Windows注册表加载配置"""
        try:
            # 打开注册表键
            key = winreg.OpenKey(winreg.HKEY_CURRENT_USER, self.registry_key)

            # 加载邮箱账号
            try:
                account, _ = winreg.QueryValueEx(key, "account")
                self.email_var.set(account)
            except FileNotFoundError:
                pass

            # 加载密码（base64编码）
            try:
                encoded_password, _ = winreg.QueryValueEx(key, "password")
                if encoded_password:
                    try:
                        decoded_password = base64.b64decode(encoded_password).decode('utf-8')
                        self.password_var.set(decoded_password)
                    except:
                        pass
            except FileNotFoundError:
                pass

            # 加载选项设置
            try:
                remember, _ = winreg.QueryValueEx(key, "remember_password")
                self.remember_password_var.set(bool(int(remember)))
            except FileNotFoundError:
                pass

            try:
                auto_copy, _ = winreg.QueryValueEx(key, "auto_copy")
                self.auto_copy_var.set(bool(int(auto_copy)))
            except FileNotFoundError:
                pass

            winreg.CloseKey(key)

        except FileNotFoundError:
            # 注册表键不存在，使用默认值
            pass
        except Exception as e:
            # 如果加载配置失败，使用默认值
            pass

    def save_config(self):
        """保存配置到Windows注册表"""
        try:
            # 创建或打开注册表键
            try:
                key = winreg.CreateKey(winreg.HKEY_CURRENT_USER, self.registry_key)
            except:
                return

            # 保存邮箱账号
            winreg.SetValueEx(key, "account", 0, winreg.REG_SZ, self.email_var.get())

            # 保存密码（如果选择记住密码）
            if self.remember_password_var.get():
                password = self.password_var.get()
                if password:
                    encoded_password = base64.b64encode(password.encode('utf-8')).decode('utf-8')
                    winreg.SetValueEx(key, "password", 0, winreg.REG_SZ, encoded_password)
                else:
                    winreg.SetValueEx(key, "password", 0, winreg.REG_SZ, "")
            else:
                winreg.SetValueEx(key, "password", 0, winreg.REG_SZ, "")

            # 保存选项设置
            winreg.SetValueEx(key, "remember_password", 0, winreg.REG_DWORD,
                             1 if self.remember_password_var.get() else 0)
            winreg.SetValueEx(key, "auto_copy", 0, winreg.REG_DWORD,
                             1 if self.auto_copy_var.get() else 0)

            winreg.CloseKey(key)

        except Exception as e:
            # 如果保存失败，忽略错误
            pass

    def run_in_thread(self, func, *args):
        """在后台线程中运行函数"""
        def wrapper():
            try:
                func(*args)
            except Exception as e:
                self.log(f"操作失败: {e}", "ERROR")
                messagebox.showerror("错误", f"操作失败: {e}")

        thread = threading.Thread(target=wrapper)
        thread.daemon = True
        thread.start()

    def one_click_reset(self):
        """一键重置功能"""
        # 确认对话框
        result = messagebox.askyesno(
            "确认操作",
            "此操作将：\n\n• 清理VS Code数据库中的Augment相关条目\n• 生成新的随机遥测ID\n• 自动备份所有修改的文件\n\n确定要继续吗？",
            icon='warning'
        )

        if result:
            self.log("开始一键重置操作...")
            self.run_in_thread(self._one_click_reset)

    def _one_click_reset(self):
        """实际的一键重置操作"""
        success_count = 0
        total_operations = 2

        # 执行数据库清理
        self.log("正在清理VS Code数据库...")
        if clean_vscode_db_gui(self.log):
            success_count += 1

        # 执行遥测ID修改
        self.log("正在修改遥测ID...")
        if modify_telemetry_ids_gui(self.log):
            success_count += 1

        # 显示结果
        if success_count == total_operations:
            self.log("一键重置完成！所有操作成功执行", "SUCCESS")
            messagebox.showinfo("操作完成", "一键重置完成！\n\n请重启VS Code以使更改生效。")
        elif success_count > 0:
            self.log(f"部分操作完成 ({success_count}/{total_operations})", "WARNING")
            messagebox.showwarning("部分完成", f"部分操作完成 ({success_count}/{total_operations})\n\n请检查日志了解详情。")
        else:
            self.log("一键重置失败", "ERROR")
            messagebox.showerror("操作失败", "一键重置失败！\n\n请检查日志了解详情。")

    def run(self):
        """运行GUI"""
        self.root.mainloop()

# GUI版本的功能函数
def clean_vscode_db_gui(log_func):
    """GUI版本的数据库清理函数"""
    log_func("开始数据库清理过程")

    # 获取VS Code路径
    paths = get_vscode_paths()
    state_db = paths["state_db"]

    if not state_db.exists():
        log_func(f"未找到VS Code数据库: {state_db}", "WARNING")
        return False

    log_func(f"找到VS Code数据库: {state_db}")

    # 创建备份
    backup_path = backup_file_gui(state_db, log_func)

    # 连接数据库
    try:
        conn = sqlite3.connect(str(state_db))
        cursor = conn.cursor()

        # 获取删除前的记录数
        cursor.execute("SELECT COUNT(*) FROM ItemTable WHERE key LIKE '%augment%'")
        count_before = cursor.fetchone()[0]

        if count_before == 0:
            log_func("数据库中未找到Augment相关条目")
            conn.close()
            return True

        # 删除包含"augment"的记录
        cursor.execute("DELETE FROM ItemTable WHERE key LIKE '%augment%'")
        conn.commit()

        # 获取删除后的记录数
        cursor.execute("SELECT COUNT(*) FROM ItemTable WHERE key LIKE '%augment%'")
        count_after = cursor.fetchone()[0]

        conn.close()

        log_func(f"已从数据库中删除 {count_before - count_after} 个Augment相关条目", "SUCCESS")
        return True

    except sqlite3.Error as e:
        log_func(f"SQLite错误: {e}", "ERROR")

        # 如果出错，从备份恢复
        if backup_path.exists():
            log_func("正在从备份恢复...")
            try:
                shutil.copy2(backup_path, state_db)
                log_func("已从备份恢复", "SUCCESS")
            except Exception as restore_error:
                log_func(f"从备份恢复失败: {restore_error}", "ERROR")

        return False
    except Exception as e:
        log_func(f"意外错误: {e}", "ERROR")
        return False

def modify_telemetry_ids_gui(log_func):
    """GUI版本的遥测ID修改函数"""
    log_func("开始VS Code遥测ID修改")

    # 获取VS Code路径
    paths = get_vscode_paths()
    storage_json = paths["storage_json"]

    if not storage_json.exists():
        log_func(f"未找到VS Code storage.json: {storage_json}", "WARNING")
        return False

    log_func(f"找到storage.json: {storage_json}")

    # 创建备份
    backup_path = backup_file_gui(storage_json, log_func)

    # 生成新ID
    log_func("正在生成新的遥测ID...")
    machine_id = generate_machine_id()
    device_id = generate_device_id()

    # 读取当前文件
    try:
        with open(storage_json, 'r', encoding='utf-8') as f:
            content = json.load(f)

        # 更新值
        content["telemetry.machineId"] = machine_id
        content["telemetry.devDeviceId"] = device_id

        # 写回文件
        with open(storage_json, 'w', encoding='utf-8') as f:
            json.dump(content, f, indent=2)

        log_func("成功更新遥测ID", "SUCCESS")
        log_func(f"新的machineId: {machine_id}")
        log_func(f"新的devDeviceId: {device_id}")
        log_func("您可能需要重启VS Code以使更改生效")

        return True

    except json.JSONDecodeError:
        log_func("存储文件不是有效的JSON格式", "ERROR")
        return False
    except Exception as e:
        log_func(f"意外错误: {e}", "ERROR")
        return False

def backup_file_gui(file_path: Path, log_func) -> Path:
    """GUI版本的文件备份函数"""
    if not file_path.exists():
        log_func(f"文件不存在: {file_path}", "ERROR")
        raise FileNotFoundError(f"文件不存在: {file_path}")

    backup_path = Path(f"{file_path}.backup")
    shutil.copy2(file_path, backup_path)
    log_func(f"已创建备份: {backup_path}", "SUCCESS")

    return backup_path

def main():
    """主函数"""
    if GUI_AVAILABLE:
        # 启动GUI界面
        app = AugmentVIPGUI()
        app.run()
    else:
        # 如果GUI不可用，显示错误信息
        print("错误: 无法启动GUI界面")
        print("请确保已安装tkinter库")
        sys.exit(1)

# 邮箱相关辅助函数
def decode_payload(payload, charset):
    """安全地用给定字符集解码负载"""
    try:
        return payload.decode(charset)
    except (UnicodeDecodeError, LookupError):
        return payload.decode('gbk', errors='ignore')

def get_clean_body_from_msg(msg):
    """解析email.message对象并返回纯净的文本正文"""
    if not EMAIL_AVAILABLE:
        return ""

    body_content, html_content = "", ""
    if msg.is_multipart():
        for part in msg.walk():
            if part.get('Content-Disposition', '').startswith('attachment'):
                continue
            payload = part.get_payload(decode=True)
            if not payload:
                continue
            charset = part.get_content_charset() or 'utf-8'
            content_type = part.get_content_type()
            if content_type == 'text/plain':
                body_content = decode_payload(payload, charset)
            elif content_type == 'text/html':
                html_content = decode_payload(payload, charset)
    else:
        if not msg.get('Content-Disposition', '').startswith('attachment'):
            payload = msg.get_payload(decode=True)
            charset = msg.get_content_charset() or 'utf-8'
            content_type = msg.get_content_type()
            if content_type == 'text/plain':
                body_content = decode_payload(payload, charset)
            elif content_type == 'text/html':
                html_content = decode_payload(payload, charset)

    if not body_content.strip() and html_content:
        try:
            soup = BeautifulSoup(html_content, 'lxml')
            return soup.get_text(separator='\n', strip=True)
        except:
            return html_content
    return body_content

def find_code_in_text(body_text):
    """使用正则表达式在字符串中查找6位验证码"""
    patterns = [r'\b\d{6}\b', r'\b\d{3}\s\d{3}\b', r'\b(?:\d\s){5}\d\b']
    for pattern in patterns:
        match = re.search(pattern, body_text)
        if match:
            return match.group(0).replace(" ", "")
    return None

def establish_baseline(server, output_queue):
    """获取当前所有邮件的UIDL，建立基线"""
    try:
        resp, uid_lines, octets = server.uidl()
        seen_uids = {line.split()[1] for line in uid_lines}
        output_queue.put(f"基线已建立，当前有 {len(seen_uids)} 封邮件。开始监控新邮件...")
        return seen_uids
    except Exception as e:
        output_queue.put(f"建立基线时出错: {e}")
        return None

def email_monitor_main(email_account, email_password, output_queue):
    """邮箱监控主逻辑"""
    if not EMAIL_AVAILABLE:
        output_queue.put("错误：缺少邮箱相关依赖包")
        return

    output_queue.put(f"正在监控邮箱: {email_account}")

    try:
        server = poplib.POP3(POP3_SERVER, port=POP3_PORT, timeout=20)
        server.user(email_account)
        server.pass_(email_password)
        output_queue.put("验证成功。")
        seen_uids = establish_baseline(server, output_queue)
        server.quit()
        if seen_uids is None:
            return
    except poplib.error_proto as e:
        output_queue.put(f"错误：登录失败。请检查凭据。 ({e})")
        return
    except Exception as e:
        output_queue.put(f"连接或建立基线时发生未知错误: {e}")
        return

    loop_counter = 0
    while True:
        try:
            try:
                if output_queue.get_nowait() == 'STOP':
                    break
            except queue.Empty:
                pass

            server = poplib.POP3(POP3_SERVER, port=POP3_PORT, timeout=20)
            server.user(email_account)
            server.pass_(email_password)

            resp, uid_lines, octets = server.uidl()

            current_uid_map = {
                parts[1]: parts[0]
                for line in uid_lines
                if len(parts := line.split()) == 2
            }

            new_uids = set(current_uid_map.keys()) - seen_uids

            if new_uids:
                loop_counter = 0
                output_queue.put(f"\n发现 {len(new_uids)} 封新邮件，正在检查...")
                new_messages = sorted(
                    [(int(current_uid_map[uid]), uid) for uid in new_uids],
                    key=lambda x: x[0],
                    reverse=True
                )

                for msg_num, uid in new_messages:
                    resp, lines, octets = server.retr(msg_num)
                    msg_content = b'\r\n'.join(lines)
                    msg = email.message_from_bytes(msg_content)

                    body = get_clean_body_from_msg(msg)
                    code = find_code_in_text(body)

                    if code:
                        output_queue.put(f"成功提取到新邮件中的验证码: {code}")
                        output_queue.put(f"VERIFICATION_CODE:{code}")
                        server.quit()
                        return

                output_queue.put("新邮件中未发现验证码，将继续监控...")
                seen_uids.update(new_uids)
            else:
                loop_counter += 1
                if loop_counter % 15 == 1:
                    output_queue.put(f"没有新邮件，继续监控... ({time.strftime('%H:%M:%S')})")

            server.quit()
            time.sleep(REFRESH_INTERVAL_SECONDS)

        except KeyboardInterrupt:
            output_queue.put("\n程序已手动停止。")
            break
        except Exception as e:
            output_queue.put(f"\n监控循环中发生错误: {e}。等待10秒后重试...")
            time.sleep(10)

if __name__ == "__main__":
    main()
