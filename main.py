#!/usr/bin/env python3
"""
Augment VIP - Windows版本集成工具
用于管理和清理VS Code数据库中与Augment相关的条目

功能:
- 清理VS Code数据库中的Augment相关条目
- 修改VS Code遥测ID以保护隐私
- 仅支持Windows系统

使用方法:
    python main.py clean        # 清理数据库
    python main.py modify-ids   # 修改遥测ID
    python main.py all          # 运行所有工具
"""

import os
import sys
import json
import sqlite3
import uuid
import shutil
import argparse
from pathlib import Path
from typing import Dict, Optional

# 版本信息
__version__ = "1.0.0"

# 控制台颜色输出
try:
    from colorama import init, Fore, Style
    init()  # 初始化colorama以支持Windows

    def info(msg: str) -> None:
        """打印信息消息"""
        print(f"{Fore.BLUE}[INFO]{Style.RESET_ALL} {msg}")

    def success(msg: str) -> None:
        """打印成功消息"""
        print(f"{Fore.GREEN}[SUCCESS]{Style.RESET_ALL} {msg}")

    def warning(msg: str) -> None:
        """打印警告消息"""
        print(f"{Fore.YELLOW}[WARNING]{Style.RESET_ALL} {msg}")

    def error(msg: str) -> None:
        """打印错误消息"""
        print(f"{Fore.RED}[ERROR]{Style.RESET_ALL} {msg}")

except ImportError:
    # 如果colorama未安装，使用普通输出
    def info(msg: str) -> None:
        print(f"[INFO] {msg}")

    def success(msg: str) -> None:
        print(f"[SUCCESS] {msg}")

    def warning(msg: str) -> None:
        print(f"[WARNING] {msg}")

    def error(msg: str) -> None:
        print(f"[ERROR] {msg}")

def get_vscode_paths() -> Dict[str, Path]:
    """
    获取Windows系统下VS Code的路径

    Returns:
        包含VS Code目录和文件路径的字典
    """
    appdata = os.environ.get("APPDATA")
    if not appdata:
        error("未找到APPDATA环境变量")
        sys.exit(1)

    base_dir = Path(appdata) / "Code" / "User"

    paths = {
        "base_dir": base_dir,
        "storage_json": base_dir / "globalStorage" / "storage.json",
        "state_db": base_dir / "globalStorage" / "state.vscdb"
    }

    return paths

def backup_file(file_path: Path) -> Path:
    """
    创建文件备份

    Args:
        file_path: 要备份的文件路径

    Returns:
        备份文件路径
    """
    if not file_path.exists():
        error(f"文件不存在: {file_path}")
        sys.exit(1)

    backup_path = Path(f"{file_path}.backup")
    shutil.copy2(file_path, backup_path)
    success(f"已创建备份: {backup_path}")

    return backup_path

def generate_machine_id() -> str:
    """生成64字符的十六进制字符串作为machineId"""
    return uuid.uuid4().hex + uuid.uuid4().hex

def generate_device_id() -> str:
    """生成UUID v4作为devDeviceId"""
    return str(uuid.uuid4())

def clean_vscode_db() -> bool:
    """
    清理VS Code数据库，删除包含"augment"的条目

    Returns:
        成功返回True，失败返回False
    """
    info("开始数据库清理过程")

    # 获取VS Code路径
    paths = get_vscode_paths()
    state_db = paths["state_db"]

    if not state_db.exists():
        warning(f"未找到VS Code数据库: {state_db}")
        return False

    info(f"找到VS Code数据库: {state_db}")

    # 创建备份
    backup_path = backup_file(state_db)

    # 连接数据库
    try:
        conn = sqlite3.connect(str(state_db))
        cursor = conn.cursor()

        # 获取删除前的记录数
        cursor.execute("SELECT COUNT(*) FROM ItemTable WHERE key LIKE '%augment%'")
        count_before = cursor.fetchone()[0]

        if count_before == 0:
            info("数据库中未找到Augment相关条目")
            conn.close()
            return True

        # 删除包含"augment"的记录
        cursor.execute("DELETE FROM ItemTable WHERE key LIKE '%augment%'")
        conn.commit()

        # 获取删除后的记录数
        cursor.execute("SELECT COUNT(*) FROM ItemTable WHERE key LIKE '%augment%'")
        count_after = cursor.fetchone()[0]

        conn.close()

        success(f"已从数据库中删除 {count_before - count_after} 个Augment相关条目")
        return True

    except sqlite3.Error as e:
        error(f"SQLite错误: {e}")

        # 如果出错，从备份恢复
        if backup_path.exists():
            info("正在从备份恢复...")
            try:
                shutil.copy2(backup_path, state_db)
                success("已从备份恢复")
            except Exception as restore_error:
                error(f"从备份恢复失败: {restore_error}")

        return False
    except Exception as e:
        error(f"意外错误: {e}")
        return False

def modify_telemetry_ids() -> bool:
    """
    修改VS Code storage.json文件中的遥测ID

    Returns:
        成功返回True，失败返回False
    """
    info("开始VS Code遥测ID修改")

    # 获取VS Code路径
    paths = get_vscode_paths()
    storage_json = paths["storage_json"]

    if not storage_json.exists():
        warning(f"未找到VS Code storage.json: {storage_json}")
        return False

    info(f"找到storage.json: {storage_json}")

    # 创建备份
    backup_path = backup_file(storage_json)

    # 生成新ID
    info("正在生成新的遥测ID...")
    machine_id = generate_machine_id()
    device_id = generate_device_id()

    # 读取当前文件
    try:
        with open(storage_json, 'r', encoding='utf-8') as f:
            content = json.load(f)

        # 更新值
        content["telemetry.machineId"] = machine_id
        content["telemetry.devDeviceId"] = device_id

        # 写回文件
        with open(storage_json, 'w', encoding='utf-8') as f:
            json.dump(content, f, indent=2)

        success("成功更新遥测ID")
        info(f"新的machineId: {machine_id}")
        info(f"新的devDeviceId: {device_id}")
        info("您可能需要重启VS Code以使更改生效")

        return True

    except json.JSONDecodeError:
        error("存储文件不是有效的JSON格式")
        return False
    except Exception as e:
        error(f"意外错误: {e}")
        return False

def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="Augment VIP - Windows版本集成工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python main.py clean        # 清理VS Code数据库
  python main.py modify-ids   # 修改遥测ID
  python main.py all          # 运行所有工具
        """
    )

    parser.add_argument('command',
                       choices=['clean', 'modify-ids', 'all'],
                       help='要执行的命令')
    parser.add_argument('--version', action='version', version=f'Augment VIP {__version__}')

    args = parser.parse_args()

    info(f"Augment VIP v{__version__} - Windows版本")

    try:
        if args.command == 'clean':
            if clean_vscode_db():
                success("数据库清理完成")
            else:
                error("数据库清理失败")
                sys.exit(1)

        elif args.command == 'modify-ids':
            if modify_telemetry_ids():
                success("遥测ID修改完成")
            else:
                error("遥测ID修改失败")
                sys.exit(1)

        elif args.command == 'all':
            info("运行所有工具...")

            clean_result = clean_vscode_db()
            modify_result = modify_telemetry_ids()

            if clean_result and modify_result:
                success("所有操作完成")
            else:
                error("部分操作失败")
                sys.exit(1)

    except Exception as e:
        error(f"意外错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()