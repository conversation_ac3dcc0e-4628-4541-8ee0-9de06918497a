#!/usr/bin/env python3
"""
Augment VIP - Windows版本可视化工具
用于管理和清理VS Code数据库中与Augment相关的条目

功能:
- 清理VS Code数据库中的Augment相关条目
- 修改VS Code遥测ID以保护隐私
- 可视化GUI界面
- 仅支持Windows系统
"""

import os
import sys
import json
import sqlite3
import uuid
import shutil
import threading
from pathlib import Path
from typing import Dict

# 版本信息
__version__ = "2.0.0"

# GUI相关导入
try:
    import tkinter as tk
    from tkinter import ttk, messagebox, scrolledtext
    GUI_AVAILABLE = True
except ImportError:
    GUI_AVAILABLE = False

# 控制台颜色输出
try:
    from colorama import init, Fore, Style
    init()  # 初始化colorama以支持Windows
    
    def info(msg: str) -> None:
        """打印信息消息"""
        print(f"{Fore.BLUE}[INFO]{Style.RESET_ALL} {msg}")
    
    def success(msg: str) -> None:
        """打印成功消息"""
        print(f"{Fore.GREEN}[SUCCESS]{Style.RESET_ALL} {msg}")
    
    def warning(msg: str) -> None:
        """打印警告消息"""
        print(f"{Fore.YELLOW}[WARNING]{Style.RESET_ALL} {msg}")
    
    def error(msg: str) -> None:
        """打印错误消息"""
        print(f"{Fore.RED}[ERROR]{Style.RESET_ALL} {msg}")
        
except ImportError:
    # 如果colorama未安装，使用普通输出
    def info(msg: str) -> None:
        print(f"[INFO] {msg}")
    
    def success(msg: str) -> None:
        print(f"[SUCCESS] {msg}")
    
    def warning(msg: str) -> None:
        print(f"[WARNING] {msg}")
    
    def error(msg: str) -> None:
        print(f"[ERROR] {msg}")

def get_vscode_paths() -> Dict[str, Path]:
    """
    获取Windows系统下VS Code的路径
    
    Returns:
        包含VS Code目录和文件路径的字典
    """
    appdata = os.environ.get("APPDATA")
    if not appdata:
        error("未找到APPDATA环境变量")
        sys.exit(1)
    
    base_dir = Path(appdata) / "Code" / "User"
    
    paths = {
        "base_dir": base_dir,
        "storage_json": base_dir / "globalStorage" / "storage.json",
        "state_db": base_dir / "globalStorage" / "state.vscdb"
    }
    
    return paths

def backup_file(file_path: Path) -> Path:
    """
    创建文件备份
    
    Args:
        file_path: 要备份的文件路径
        
    Returns:
        备份文件路径
    """
    if not file_path.exists():
        error(f"文件不存在: {file_path}")
        sys.exit(1)
        
    backup_path = Path(f"{file_path}.backup")
    shutil.copy2(file_path, backup_path)
    success(f"已创建备份: {backup_path}")
    
    return backup_path

def generate_machine_id() -> str:
    """生成64字符的十六进制字符串作为machineId"""
    return uuid.uuid4().hex + uuid.uuid4().hex

def generate_device_id() -> str:
    """生成UUID v4作为devDeviceId"""
    return str(uuid.uuid4())

def clean_vscode_db() -> bool:
    """
    清理VS Code数据库，删除包含"augment"的条目
    
    Returns:
        成功返回True，失败返回False
    """
    info("开始数据库清理过程")
    
    # 获取VS Code路径
    paths = get_vscode_paths()
    state_db = paths["state_db"]
    
    if not state_db.exists():
        warning(f"未找到VS Code数据库: {state_db}")
        return False
    
    info(f"找到VS Code数据库: {state_db}")
    
    # 创建备份
    backup_path = backup_file(state_db)
    
    # 连接数据库
    try:
        conn = sqlite3.connect(str(state_db))
        cursor = conn.cursor()
        
        # 获取删除前的记录数
        cursor.execute("SELECT COUNT(*) FROM ItemTable WHERE key LIKE '%augment%'")
        count_before = cursor.fetchone()[0]
        
        if count_before == 0:
            info("数据库中未找到Augment相关条目")
            conn.close()
            return True
        
        # 删除包含"augment"的记录
        cursor.execute("DELETE FROM ItemTable WHERE key LIKE '%augment%'")
        conn.commit()
        
        # 获取删除后的记录数
        cursor.execute("SELECT COUNT(*) FROM ItemTable WHERE key LIKE '%augment%'")
        count_after = cursor.fetchone()[0]
        
        conn.close()
        
        success(f"已从数据库中删除 {count_before - count_after} 个Augment相关条目")
        return True
        
    except sqlite3.Error as e:
        error(f"SQLite错误: {e}")
        
        # 如果出错，从备份恢复
        if backup_path.exists():
            info("正在从备份恢复...")
            try:
                shutil.copy2(backup_path, state_db)
                success("已从备份恢复")
            except Exception as restore_error:
                error(f"从备份恢复失败: {restore_error}")
        
        return False
    except Exception as e:
        error(f"意外错误: {e}")
        return False

def modify_telemetry_ids() -> bool:
    """
    修改VS Code storage.json文件中的遥测ID
    
    Returns:
        成功返回True，失败返回False
    """
    info("开始VS Code遥测ID修改")
    
    # 获取VS Code路径
    paths = get_vscode_paths()
    storage_json = paths["storage_json"]
    
    if not storage_json.exists():
        warning(f"未找到VS Code storage.json: {storage_json}")
        return False
    
    info(f"找到storage.json: {storage_json}")
    
    # 创建备份
    backup_path = backup_file(storage_json)
    
    # 生成新ID
    info("正在生成新的遥测ID...")
    machine_id = generate_machine_id()
    device_id = generate_device_id()
    
    # 读取当前文件
    try:
        with open(storage_json, 'r', encoding='utf-8') as f:
            content = json.load(f)
        
        # 更新值
        content["telemetry.machineId"] = machine_id
        content["telemetry.devDeviceId"] = device_id
        
        # 写回文件
        with open(storage_json, 'w', encoding='utf-8') as f:
            json.dump(content, f, indent=2)
        
        success("成功更新遥测ID")
        info(f"新的machineId: {machine_id}")
        info(f"新的devDeviceId: {device_id}")
        info("您可能需要重启VS Code以使更改生效")
        
        return True
        
    except json.JSONDecodeError:
        error("存储文件不是有效的JSON格式")
        return False
    except Exception as e:
        error(f"意外错误: {e}")
        return False

class AugmentVIPGUI:
    """Augment VIP 可视化界面"""

    def __init__(self):
        self.root = tk.Tk()
        self.root.title(f"Augment VIP v{__version__}")
        self.root.geometry("500x400")
        self.root.minsize(400, 300)  # 设置最小尺寸
        self.root.resizable(True, True)  # 允许调整大小

        # 设置图标和样式
        self.setup_styles()
        self.create_widgets()

    def setup_styles(self):
        """设置界面样式"""
        style = ttk.Style()
        style.theme_use('clam')

        # 配置按钮样式
        style.configure('Reset.TButton', font=('微软雅黑', 14, 'bold'), padding=20)
        style.configure('Title.TLabel', font=('微软雅黑', 18, 'bold'))
        style.configure('Info.TLabel', font=('微软雅黑', 10))

    def create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="30")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 标题
        title_label = ttk.Label(main_frame, text="Augment VIP", style='Title.TLabel')
        title_label.grid(row=0, column=0, pady=(0, 10))

        subtitle_label = ttk.Label(main_frame, text="VS Code 一键重置工具", style='Info.TLabel')
        subtitle_label.grid(row=1, column=0, pady=(0, 30))

        # 功能说明
        info_text = """此工具将执行以下操作：
• 清理VS Code数据库中的Augment相关条目
• 生成新的随机遥测ID
• 自动备份所有修改的文件

注意：使用前请确保VS Code已完全关闭"""

        info_label = ttk.Label(main_frame, text=info_text, style='Info.TLabel', justify='left')
        info_label.grid(row=2, column=0, pady=(0, 30))

        # 按钮区域框架
        button_area_frame = ttk.Frame(main_frame)
        button_area_frame.grid(row=3, column=0, pady=(0, 20), sticky=(tk.W, tk.E))
        button_area_frame.columnconfigure(0, weight=1)
        button_area_frame.columnconfigure(1, weight=1)

        # 一键重置按钮
        reset_btn = ttk.Button(button_area_frame, text="一键重置",
                              command=self.one_click_reset, style='Reset.TButton')
        reset_btn.grid(row=0, column=0, padx=(0, 10), sticky=(tk.W, tk.E))

        # 邮箱接收按钮
        email_btn = ttk.Button(button_area_frame, text="邮箱接收",
                              command=self.open_email_tool, style='Reset.TButton')
        email_btn.grid(row=0, column=1, padx=(10, 0), sticky=(tk.W, tk.E))

        # 日志输出区域
        log_frame = ttk.LabelFrame(main_frame, text="操作日志", padding="10")
        log_frame.grid(row=4, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 15))

        # 日志文本框
        self.log_text = scrolledtext.ScrolledText(log_frame, height=8, width=60,
                                                 font=('Consolas', 9), wrap=tk.WORD)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 底部按钮
        bottom_frame = ttk.Frame(main_frame)
        bottom_frame.grid(row=5, column=0, sticky=(tk.W, tk.E), pady=(10, 0))

        # 清空日志按钮
        clear_btn = ttk.Button(bottom_frame, text="清空日志", command=self.clear_log)
        clear_btn.grid(row=0, column=0, padx=(0, 10))

        # 退出按钮
        exit_btn = ttk.Button(bottom_frame, text="退出", command=self.root.quit)
        exit_btn.grid(row=0, column=1)

        # 配置权重，使组件能够随窗口大小调整
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(4, weight=1)  # 日志区域可以扩展
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)

        # 初始化日志
        self.log("欢迎使用 Augment VIP v" + __version__)
        self.log("点击'一键重置'开始操作")

    def log(self, message, level="INFO"):
        """添加日志消息"""
        import datetime
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        log_message = f"[{timestamp}] [{level}] {message}\n"

        self.log_text.insert(tk.END, log_message)
        self.log_text.see(tk.END)
        self.root.update()

    def clear_log(self):
        """清空日志"""
        self.log_text.delete(1.0, tk.END)

    def open_email_tool(self):
        """打开邮箱接收工具"""
        import subprocess
        import os

        try:
            # 检查是否在打包环境中运行
            if getattr(sys, 'frozen', False):
                # 在打包的exe中运行，需要先提取文件到临时目录
                import tempfile
                script_dir = sys._MEIPASS
                source_script = os.path.join(script_dir, "邮箱接收.py")

                # 创建临时文件
                temp_dir = tempfile.gettempdir()
                email_script = os.path.join(temp_dir, "邮箱接收.py")

                # 复制文件到临时目录
                if os.path.exists(source_script):
                    shutil.copy2(source_script, email_script)
                else:
                    self.log("打包文件中未找到邮箱接收.py", "ERROR")
                    messagebox.showerror("错误", "打包文件中未找到邮箱接收.py")
                    return
            else:
                # 在开发环境中运行
                script_dir = os.path.dirname(os.path.abspath(__file__))
                email_script = os.path.join(script_dir, "邮箱接收.py")

            if os.path.exists(email_script):
                # 在新进程中运行邮箱接收工具
                subprocess.Popen([sys.executable, email_script],
                               creationflags=subprocess.CREATE_NEW_CONSOLE if os.name == 'nt' else 0)
                self.log("已启动邮箱接收工具", "SUCCESS")
            else:
                self.log("未找到邮箱接收.py文件", "ERROR")
                messagebox.showerror("错误", "未找到邮箱接收.py文件")
        except Exception as e:
            self.log(f"启动邮箱接收工具失败: {e}", "ERROR")
            messagebox.showerror("错误", f"启动邮箱接收工具失败: {e}")

    def run_in_thread(self, func, *args):
        """在后台线程中运行函数"""
        def wrapper():
            try:
                func(*args)
            except Exception as e:
                self.log(f"操作失败: {e}", "ERROR")
                messagebox.showerror("错误", f"操作失败: {e}")

        thread = threading.Thread(target=wrapper)
        thread.daemon = True
        thread.start()

    def one_click_reset(self):
        """一键重置功能"""
        # 确认对话框
        result = messagebox.askyesno(
            "确认操作",
            "此操作将：\n\n• 清理VS Code数据库中的Augment相关条目\n• 生成新的随机遥测ID\n• 自动备份所有修改的文件\n\n确定要继续吗？",
            icon='warning'
        )

        if result:
            self.log("开始一键重置操作...")
            self.run_in_thread(self._one_click_reset)

    def _one_click_reset(self):
        """实际的一键重置操作"""
        success_count = 0
        total_operations = 2

        # 执行数据库清理
        self.log("正在清理VS Code数据库...")
        if clean_vscode_db_gui(self.log):
            success_count += 1

        # 执行遥测ID修改
        self.log("正在修改遥测ID...")
        if modify_telemetry_ids_gui(self.log):
            success_count += 1

        # 显示结果
        if success_count == total_operations:
            self.log("一键重置完成！所有操作成功执行", "SUCCESS")
            messagebox.showinfo("操作完成", "一键重置完成！\n\n请重启VS Code以使更改生效。")
        elif success_count > 0:
            self.log(f"部分操作完成 ({success_count}/{total_operations})", "WARNING")
            messagebox.showwarning("部分完成", f"部分操作完成 ({success_count}/{total_operations})\n\n请检查日志了解详情。")
        else:
            self.log("一键重置失败", "ERROR")
            messagebox.showerror("操作失败", "一键重置失败！\n\n请检查日志了解详情。")

    def run(self):
        """运行GUI"""
        self.root.mainloop()

# GUI版本的功能函数
def clean_vscode_db_gui(log_func):
    """GUI版本的数据库清理函数"""
    log_func("开始数据库清理过程")

    # 获取VS Code路径
    paths = get_vscode_paths()
    state_db = paths["state_db"]

    if not state_db.exists():
        log_func(f"未找到VS Code数据库: {state_db}", "WARNING")
        return False

    log_func(f"找到VS Code数据库: {state_db}")

    # 创建备份
    backup_path = backup_file_gui(state_db, log_func)

    # 连接数据库
    try:
        conn = sqlite3.connect(str(state_db))
        cursor = conn.cursor()

        # 获取删除前的记录数
        cursor.execute("SELECT COUNT(*) FROM ItemTable WHERE key LIKE '%augment%'")
        count_before = cursor.fetchone()[0]

        if count_before == 0:
            log_func("数据库中未找到Augment相关条目")
            conn.close()
            return True

        # 删除包含"augment"的记录
        cursor.execute("DELETE FROM ItemTable WHERE key LIKE '%augment%'")
        conn.commit()

        # 获取删除后的记录数
        cursor.execute("SELECT COUNT(*) FROM ItemTable WHERE key LIKE '%augment%'")
        count_after = cursor.fetchone()[0]

        conn.close()

        log_func(f"已从数据库中删除 {count_before - count_after} 个Augment相关条目", "SUCCESS")
        return True

    except sqlite3.Error as e:
        log_func(f"SQLite错误: {e}", "ERROR")

        # 如果出错，从备份恢复
        if backup_path.exists():
            log_func("正在从备份恢复...")
            try:
                shutil.copy2(backup_path, state_db)
                log_func("已从备份恢复", "SUCCESS")
            except Exception as restore_error:
                log_func(f"从备份恢复失败: {restore_error}", "ERROR")

        return False
    except Exception as e:
        log_func(f"意外错误: {e}", "ERROR")
        return False

def modify_telemetry_ids_gui(log_func):
    """GUI版本的遥测ID修改函数"""
    log_func("开始VS Code遥测ID修改")

    # 获取VS Code路径
    paths = get_vscode_paths()
    storage_json = paths["storage_json"]

    if not storage_json.exists():
        log_func(f"未找到VS Code storage.json: {storage_json}", "WARNING")
        return False

    log_func(f"找到storage.json: {storage_json}")

    # 创建备份
    backup_path = backup_file_gui(storage_json, log_func)

    # 生成新ID
    log_func("正在生成新的遥测ID...")
    machine_id = generate_machine_id()
    device_id = generate_device_id()

    # 读取当前文件
    try:
        with open(storage_json, 'r', encoding='utf-8') as f:
            content = json.load(f)

        # 更新值
        content["telemetry.machineId"] = machine_id
        content["telemetry.devDeviceId"] = device_id

        # 写回文件
        with open(storage_json, 'w', encoding='utf-8') as f:
            json.dump(content, f, indent=2)

        log_func("成功更新遥测ID", "SUCCESS")
        log_func(f"新的machineId: {machine_id}")
        log_func(f"新的devDeviceId: {device_id}")
        log_func("您可能需要重启VS Code以使更改生效")

        return True

    except json.JSONDecodeError:
        log_func("存储文件不是有效的JSON格式", "ERROR")
        return False
    except Exception as e:
        log_func(f"意外错误: {e}", "ERROR")
        return False

def backup_file_gui(file_path: Path, log_func) -> Path:
    """GUI版本的文件备份函数"""
    if not file_path.exists():
        log_func(f"文件不存在: {file_path}", "ERROR")
        raise FileNotFoundError(f"文件不存在: {file_path}")

    backup_path = Path(f"{file_path}.backup")
    shutil.copy2(file_path, backup_path)
    log_func(f"已创建备份: {backup_path}", "SUCCESS")

    return backup_path

def main():
    """主函数"""
    if GUI_AVAILABLE:
        # 启动GUI界面
        app = AugmentVIPGUI()
        app.run()
    else:
        # 如果GUI不可用，显示错误信息
        print("错误: 无法启动GUI界面")
        print("请确保已安装tkinter库")
        sys.exit(1)

if __name__ == "__main__":
    main()
