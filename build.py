#!/usr/bin/env python3
"""
Augment VIP 打包脚本
使用PyInstaller将main.py打包成exe文件

使用方法:
    python build.py
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path

def info(msg):
    """打印信息"""
    print(f"[INFO] {msg}")

def success(msg):
    """打印成功信息"""
    print(f"[SUCCESS] {msg}")

def error(msg):
    """打印错误信息"""
    print(f"[ERROR] {msg}")

def warning(msg):
    """打印警告信息"""
    print(f"[WARNING] {msg}")

def check_pyinstaller():
    """检查PyInstaller是否已安装"""
    try:
        import PyInstaller
        info(f"PyInstaller已安装，版本: {PyInstaller.__version__}")
        return True
    except ImportError:
        error("PyInstaller未安装")
        return False

def install_pyinstaller():
    """安装PyInstaller"""
    info("正在安装PyInstaller...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
        success("PyInstaller安装成功")
        return True
    except subprocess.CalledProcessError as e:
        error(f"PyInstaller安装失败: {e}")
        return False

def clean_build_dirs():
    """清理构建目录"""
    dirs_to_clean = ["build", "dist", "__pycache__"]
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            info(f"清理目录: {dir_name}")
            shutil.rmtree(dir_name)

def create_spec_file():
    """创建PyInstaller spec文件"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=[],
    hiddenimports=['colorama'],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='augment-vip',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,
)
'''
    
    with open('augment-vip.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    info("已创建spec文件: augment-vip.spec")

def build_exe():
    """构建exe文件"""
    info("开始构建exe文件...")
    
    try:
        # 使用spec文件构建
        subprocess.check_call([
            sys.executable, "-m", "PyInstaller",
            "--clean",
            "augment-vip.spec"
        ])
        success("exe文件构建成功")
        return True
    except subprocess.CalledProcessError as e:
        error(f"构建失败: {e}")
        return False

def copy_additional_files():
    """复制额外文件到dist目录"""
    dist_dir = Path("dist")
    if not dist_dir.exists():
        error("dist目录不存在")
        return False
    
    files_to_copy = [
        "README.md",
        "使用说明.txt",
        "requirements.txt"
    ]
    
    for file_name in files_to_copy:
        if os.path.exists(file_name):
            shutil.copy2(file_name, dist_dir)
            info(f"已复制: {file_name}")
    
    return True

def create_batch_files():
    """创建批处理文件"""
    dist_dir = Path("dist")
    
    # 创建启动脚本
    batch_content = '''@echo off
chcp 65001 >nul
echo Augment VIP - Windows版本
echo ========================
echo.
echo 请选择操作:
echo 1. 清理VS Code数据库
echo 2. 修改遥测ID
echo 3. 运行所有工具
echo 4. 查看帮助
echo 5. 退出
echo.
set /p choice="请输入选择 (1-5): "

if "%choice%"=="1" (
    augment-vip.exe clean
) else if "%choice%"=="2" (
    augment-vip.exe modify-ids
) else if "%choice%"=="3" (
    augment-vip.exe all
) else if "%choice%"=="4" (
    augment-vip.exe --help
) else if "%choice%"=="5" (
    exit
) else (
    echo 无效选择，请重新运行
)

echo.
pause
'''
    
    batch_file = dist_dir / "启动.bat"
    with open(batch_file, 'w', encoding='gbk') as f:
        f.write(batch_content)
    
    info("已创建启动脚本: 启动.bat")
    
    # 创建直接命令脚本
    direct_scripts = {
        "清理数据库.bat": "augment-vip.exe clean\npause",
        "修改ID.bat": "augment-vip.exe modify-ids\npause",
        "运行所有.bat": "augment-vip.exe all\npause"
    }
    
    for script_name, content in direct_scripts.items():
        script_file = dist_dir / script_name
        with open(script_file, 'w', encoding='gbk') as f:
            f.write(f"@echo off\nchcp 65001 >nul\n{content}")
        info(f"已创建脚本: {script_name}")

def main():
    """主函数"""
    info("开始构建Augment VIP exe文件")
    info("=" * 50)
    
    # 检查当前目录
    if not os.path.exists("main.py"):
        error("未找到main.py文件，请在项目根目录运行此脚本")
        sys.exit(1)
    
    # 检查并安装PyInstaller
    if not check_pyinstaller():
        if not install_pyinstaller():
            sys.exit(1)
    
    # 清理构建目录
    clean_build_dirs()
    
    # 创建spec文件
    create_spec_file()
    
    # 构建exe
    if not build_exe():
        sys.exit(1)
    
    # 复制额外文件
    copy_additional_files()
    
    # 创建批处理文件
    create_batch_files()
    
    # 显示结果
    success("构建完成！")
    info("=" * 50)
    info("构建结果:")
    info("- exe文件: dist/augment-vip.exe")
    info("- 启动脚本: dist/启动.bat")
    info("- 直接命令脚本: dist/清理数据库.bat, dist/修改ID.bat, dist/运行所有.bat")
    info("- 文档文件: dist/README.md, dist/使用说明.txt")
    info("")
    info("使用方法:")
    info("1. 进入dist目录")
    info("2. 双击'启动.bat'使用交互式菜单")
    info("3. 或直接双击对应的功能脚本")
    info("4. 或在命令行中运行: augment-vip.exe [命令]")

if __name__ == "__main__":
    main()
