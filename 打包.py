#!/usr/bin/env python3
"""
Augment VIP 打包脚本
将main.py打包成独立的exe文件

使用方法:
    python 打包.py
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path

def 打印信息(消息):
    """打印信息"""
    print(f"[信息] {消息}")

def 打印成功(消息):
    """打印成功信息"""
    print(f"[成功] {消息}")

def 打印错误(消息):
    """打印错误信息"""
    print(f"[错误] {消息}")

def 打印警告(消息):
    """打印警告信息"""
    print(f"[警告] {消息}")

def 检查环境():
    """检查Python和必要工具"""
    打印信息("检查Python环境...")

    # 检查Python版本
    if sys.version_info < (3, 6):
        打印错误("需要Python 3.6或更高版本")
        return False

    打印成功(f"Python版本: {sys.version}")

    # 检查PyInstaller
    try:
        import PyInstaller
        打印成功(f"PyInstaller已安装，版本: {PyInstaller.__version__}")
        return True
    except ImportError:
        打印警告("PyInstaller未安装，正在安装...")
        return 安装依赖()

def 安装依赖():
    """安装必要的依赖包"""
    依赖包 = ["pyinstaller", "colorama"]

    for 包名 in 依赖包:
        打印信息(f"安装 {包名}...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", 包名])
            打印成功(f"{包名} 安装成功")
        except subprocess.CalledProcessError as e:
            打印错误(f"{包名} 安装失败: {e}")
            return False

    return True

def 清理旧文件():
    """清理之前的构建文件"""
    打印信息("清理旧的构建文件...")

    要清理的目录 = ["build", "dist", "__pycache__"]

    for 目录 in 要清理的目录:
        if os.path.exists(目录):
            打印信息(f"删除目录: {目录}")
            shutil.rmtree(目录)

    # 删除spec文件
    import glob
    for spec文件 in glob.glob("*.spec"):
        打印信息(f"删除文件: {spec文件}")
        os.remove(spec文件)

def 执行打包():
    """执行PyInstaller打包"""
    打印信息("开始打包exe文件...")

    # PyInstaller命令参数
    命令 = [
        sys.executable, "-m", "PyInstaller",
        "--onefile",                    # 打包成单个文件
        "--console",                    # 控制台应用
        "--name", "augment-vip",        # 输出文件名
        "--clean",                      # 清理缓存
        "--noconfirm",                  # 不询问覆盖
        "main.py"                       # 源文件
    ]

    try:
        subprocess.check_call(命令)
        打印成功("exe文件打包成功")
        return True
    except subprocess.CalledProcessError as e:
        打印错误(f"打包失败: {e}")
        return False

def 创建启动脚本():
    """创建方便使用的启动脚本"""
    打印信息("创建启动脚本...")

    dist目录 = Path("dist")
    if not dist目录.exists():
        打印错误("dist目录不存在")
        return False

    # 创建交互式启动脚本
    启动脚本内容 = '''@echo off
chcp 65001 >nul
title Augment VIP - Windows版本
echo.
echo ╔══════════════════════════════════════╗
echo ║          Augment VIP v1.0.0          ║
echo ║        Windows版本集成工具           ║
echo ╚══════════════════════════════════════╝
echo.
echo 请选择要执行的操作:
echo.
echo [1] 清理VS Code数据库
echo [2] 修改VS Code遥测ID
echo [3] 运行所有工具
echo [4] 查看帮助信息
echo [5] 退出程序
echo.
set /p choice="请输入选择 (1-5): "

if "%choice%"=="1" (
    echo.
    echo 正在清理VS Code数据库...
    augment-vip.exe clean
) else if "%choice%"=="2" (
    echo.
    echo 正在修改VS Code遥测ID...
    augment-vip.exe modify-ids
) else if "%choice%"=="3" (
    echo.
    echo 正在运行所有工具...
    augment-vip.exe all
) else if "%choice%"=="4" (
    echo.
    augment-vip.exe --help
) else if "%choice%"=="5" (
    echo 再见！
    exit
) else (
    echo.
    echo 无效的选择，请重新运行脚本
)

echo.
echo 操作完成，按任意键退出...
pause >nul
'''

    # 写入启动脚本
    启动脚本文件 = dist目录 / "启动.bat"
    with open(启动脚本文件, 'w', encoding='gbk') as f:
        f.write(启动脚本内容)

    打印成功("启动脚本创建成功: 启动.bat")

    # 创建快捷功能脚本
    快捷脚本 = {
        "清理数据库.bat": "@echo off\nchcp 65001 >nul\necho 正在清理VS Code数据库...\naugment-vip.exe clean\necho.\necho 操作完成！\npause",
        "修改遥测ID.bat": "@echo off\nchcp 65001 >nul\necho 正在修改VS Code遥测ID...\naugment-vip.exe modify-ids\necho.\necho 操作完成！\npause",
        "运行所有功能.bat": "@echo off\nchcp 65001 >nul\necho 正在运行所有功能...\naugment-vip.exe all\necho.\necho 操作完成！\npause"
    }

    for 脚本名, 脚本内容 in 快捷脚本.items():
        脚本文件 = dist目录 / 脚本名
        with open(脚本文件, 'w', encoding='gbk') as f:
            f.write(脚本内容)
        打印成功(f"快捷脚本创建成功: {脚本名}")

    return True

def 复制文档():
    """复制说明文档到dist目录"""
    打印信息("复制文档文件...")

    dist目录 = Path("dist")
    文档文件 = ["README.md", "使用说明.txt", "发布说明.md"]

    for 文件名 in 文档文件:
        if os.path.exists(文件名):
            目标文件 = dist目录 / 文件名
            shutil.copy2(文件名, 目标文件)
            打印成功(f"已复制: {文件名}")

def 显示结果():
    """显示打包结果"""
    打印成功("=" * 50)
    打印成功("🎉 打包完成！")
    打印成功("=" * 50)

    print("\n📁 输出文件:")
    print("   dist/augment-vip.exe     - 主程序")
    print("   dist/启动.bat           - 交互式启动")
    print("   dist/清理数据库.bat     - 直接清理数据库")
    print("   dist/修改遥测ID.bat     - 直接修改ID")
    print("   dist/运行所有功能.bat   - 运行所有功能")

    print("\n🚀 使用方法:")
    print("   1. 进入 dist 目录")
    print("   2. 双击 '启动.bat' 使用交互菜单")
    print("   3. 或双击对应功能的bat文件")
    print("   4. 或在命令行运行: augment-vip.exe [命令]")

    print("\n💡 提示:")
    print("   - 使用前请确保VS Code已关闭")
    print("   - 建议以管理员权限运行")
    print("   - 所有操作都会自动创建备份")

def 主函数():
    """主函数"""
    print("🔧 Augment VIP 打包工具")
    print("=" * 30)

    # 检查源文件
    if not os.path.exists("main.py"):
        打印错误("未找到 main.py 文件")
        打印错误("请在项目根目录运行此脚本")
        input("按回车键退出...")
        sys.exit(1)

    try:
        # 执行打包流程
        if not 检查环境():
            sys.exit(1)

        清理旧文件()

        if not 执行打包():
            sys.exit(1)

        创建启动脚本()
        复制文档()
        显示结果()

        print("\n✅ 打包流程全部完成！")
        input("按回车键退出...")

    except KeyboardInterrupt:
        打印警告("\n用户取消操作")
        sys.exit(1)
    except Exception as e:
        打印错误(f"发生意外错误: {e}")
        input("按回车键退出...")
        sys.exit(1)

if __name__ == "__main__":
    主函数()